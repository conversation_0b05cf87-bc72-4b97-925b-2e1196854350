<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص خطأ Content Script</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .error-box {
            background: #fee;
            border: 1px solid #fcc;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            direction: ltr;
            text-align: left;
        }
        .solution {
            background: #efe;
            border: 1px solid #cfc;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .extension-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 تشخيص وحل خطأ Content Script</h1>
        
        <div class="error-box">
            <strong>الخطأ المبلغ عنه:</strong><br>
            content_script.js:1 Uncaught TypeError: Cannot read properties of null (reading 'deref')<br>
            at MutationObserver.&lt;anonymous&gt; (content_script.js:1:419650)
        </div>

        <h2>📋 تشخيص المشكلة</h2>
        
        <div class="warning">
            <strong>⚠️ ملاحظة مهمة:</strong><br>
            هذا الخطأ ليس من كود مشروعك، بل من إضافة متصفح (Browser Extension) مثبتة على المتصفح.
        </div>

        <h3>🔍 خطوات التشخيص</h3>
        
        <div class="step">
            <strong>1. فحص الإضافات المثبتة:</strong>
            <button onclick="checkExtensions()">فحص الإضافات</button>
            <div id="extensionsList" class="extension-list" style="display:none;">
                <p>الإضافات المشتبه بها:</p>
                <ul>
                    <li>مدراء كلمات المرور (LastPass, 1Password, Bitwarden)</li>
                    <li>أدوات الترجمة (Google Translate)</li>
                    <li>حاجبات الإعلانات (AdBlock, uBlock Origin)</li>
                    <li>أدوات التطوير (React DevTools, Vue DevTools)</li>
                    <li>إضافات الأمان (Malwarebytes, Kaspersky)</li>
                </ul>
            </div>
        </div>

        <div class="step">
            <strong>2. اختبار في وضع التصفح الخفي:</strong>
            <button onclick="testIncognito()">إرشادات الاختبار</button>
            <div id="incognitoTest" style="display:none;">
                <p>افتح الموقع في وضع التصفح الخفي (Ctrl+Shift+N) وتحقق من اختفاء الخطأ</p>
            </div>
        </div>

        <div class="step">
            <strong>3. تعطيل الإضافات تدريجياً:</strong>
            <button onclick="showDisableSteps()">عرض الخطوات</button>
            <div id="disableSteps" style="display:none;">
                <ol>
                    <li>اذهب إلى chrome://extensions/</li>
                    <li>عطل جميع الإضافات</li>
                    <li>أعد تحميل الموقع</li>
                    <li>فعل الإضافات واحدة تلو الأخرى لتحديد المسببة للمشكلة</li>
                </ol>
            </div>
        </div>

        <h2>🛠️ الحلول المقترحة</h2>

        <div class="solution">
            <h3>الحل 1: حماية كود الموقع من تداخل الإضافات</h3>
            <p>إضافة كود حماية في مشروعك لمنع تداخل إضافات المتصفح:</p>
            <button onclick="showProtectionCode()">عرض الكود</button>
            <div id="protectionCode" style="display:none;">
                <pre style="background:#f8f9fa; padding:10px; border-radius:5px; overflow-x:auto;">
// إضافة هذا الكود في index.html أو main.tsx
window.addEventListener('error', function(e) {
  // تجاهل أخطاء content scripts من الإضافات
  if (e.filename && e.filename.includes('content_script')) {
    e.preventDefault();
    console.warn('تم تجاهل خطأ من إضافة متصفح:', e.message);
    return true;
  }
});

// حماية MutationObserver من التداخل
const originalMutationObserver = window.MutationObserver;
window.MutationObserver = function(callback) {
  return new originalMutationObserver(function(mutations, observer) {
    try {
      callback.call(this, mutations, observer);
    } catch (error) {
      if (error.message.includes('deref')) {
        console.warn('تم تجاهل خطأ MutationObserver من إضافة:', error.message);
        return;
      }
      throw error;
    }
  });
};
                </pre>
            </div>
        </div>

        <div class="solution">
            <h3>الحل 2: تحسين أمان النماذج</h3>
            <p>تعزيز الحماية ضد تداخل إضافات مدراء كلمات المرور:</p>
            <button onclick="showFormProtection()">عرض الكود</button>
            <div id="formProtection" style="display:none;">
                <pre style="background:#f8f9fa; padding:10px; border-radius:5px; overflow-x:auto;">
// حماية النماذج من التداخل
document.addEventListener('DOMContentLoaded', function() {
  // منع إضافات مدراء كلمات المرور من التداخل
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    form.setAttribute('data-form-type', 'other');
    
    const inputs = form.querySelectorAll('input[type="password"]');
    inputs.forEach(input => {
      input.setAttribute('data-lpignore', 'true');
      input.setAttribute('data-1p-ignore', 'true');
      input.setAttribute('data-bwignore', 'true');
    });
  });
});
                </pre>
            </div>
        </div>

        <div class="solution">
            <h3>الحل 3: إضافة معالج أخطاء عام</h3>
            <p>معالج شامل لجميع أخطاء الإضافات:</p>
            <button onclick="showErrorHandler()">عرض الكود</button>
            <div id="errorHandler" style="display:none;">
                <pre style="background:#f8f9fa; padding:10px; border-radius:5px; overflow-x:auto;">
// معالج أخطاء شامل
(function() {
  'use strict';
  
  // قائمة بأسماء ملفات الإضافات الشائعة
  const extensionFiles = [
    'content_script',
    'contentscript',
    'inject',
    'extension',
    'chrome-extension',
    'moz-extension'
  ];
  
  // معالج الأخطاء العام
  window.addEventListener('error', function(event) {
    const isExtensionError = extensionFiles.some(file => 
      event.filename && event.filename.includes(file)
    );
    
    if (isExtensionError) {
      console.warn('🔧 تم تجاهل خطأ من إضافة متصفح:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno
      });
      event.preventDefault();
      return true;
    }
  }, true);
  
  // معالج الأخطاء غير المعالجة
  window.addEventListener('unhandledrejection', function(event) {
    if (event.reason && event.reason.stack) {
      const isExtensionError = extensionFiles.some(file => 
        event.reason.stack.includes(file)
      );
      
      if (isExtensionError) {
        console.warn('🔧 تم تجاهل Promise rejection من إضافة متصفح:', event.reason);
        event.preventDefault();
        return true;
      }
    }
  });
})();
                </pre>
            </div>
        </div>

        <h2>🎯 التطبيق العملي</h2>
        
        <div class="step">
            <strong>للمطورين:</strong>
            <button onclick="implementSolution()">تطبيق الحل</button>
            <div id="implementationSteps" style="display:none;">
                <ol>
                    <li>أضف معالج الأخطاء في ملف <code>index.html</code></li>
                    <li>أضف حماية النماذج في مكونات React</li>
                    <li>اختبر الموقع مع إضافات مختلفة</li>
                    <li>راقب وحدة التحكم للتأكد من عدم ظهور الأخطاء</li>
                </ol>
            </div>
        </div>

        <div class="step">
            <strong>للمستخدمين:</strong>
            <button onclick="showUserSolutions()">حلول المستخدمين</button>
            <div id="userSolutions" style="display:none;">
                <ul>
                    <li>تحديث إضافات المتصفح لأحدث إصدار</li>
                    <li>تعطيل الإضافات غير الضرورية</li>
                    <li>استخدام وضع التصفح الخفي للاختبار</li>
                    <li>تنظيف ذاكرة التخزين المؤقت للمتصفح</li>
                </ul>
            </div>
        </div>

        <div class="warning">
            <strong>📝 ملاحظة:</strong><br>
            هذا الخطأ لا يؤثر على وظائف موقعك الأساسية، لكن قد يسبب رسائل خطأ مزعجة في وحدة التحكم.
        </div>
    </div>

    <script>
        function checkExtensions() {
            document.getElementById('extensionsList').style.display = 'block';
        }
        
        function testIncognito() {
            document.getElementById('incognitoTest').style.display = 'block';
        }
        
        function showDisableSteps() {
            document.getElementById('disableSteps').style.display = 'block';
        }
        
        function showProtectionCode() {
            document.getElementById('protectionCode').style.display = 'block';
        }
        
        function showFormProtection() {
            document.getElementById('formProtection').style.display = 'block';
        }
        
        function showErrorHandler() {
            document.getElementById('errorHandler').style.display = 'block';
        }
        
        function implementSolution() {
            document.getElementById('implementationSteps').style.display = 'block';
        }
        
        function showUserSolutions() {
            document.getElementById('userSolutions').style.display = 'block';
        }
    </script>
</body>
</html>
