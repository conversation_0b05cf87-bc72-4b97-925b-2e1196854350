{"common": {"welcome": "Welcome", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "search": "Search", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "loading": "Loading...", "error": "An error occurred", "success": "Success", "required": "This field is required", "email": "Email", "password": "Password", "name": "Name", "age": "Age", "location": "Location", "submit": "Submit", "backToHome": "Back to Home", "dir": "ltr"}, "navigation": {"home": "Home", "search": "Search", "messages": "Messages", "profile": "Profile", "settings": "Settings", "help": "Help", "features": "Features", "about": "About Us", "contact": "Contact Us", "helpCenter": "Help Center", "faq": "FAQ", "islamicGuidelines": "Islamic Guidelines", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "security": "Security & Privacy", "logout": "Logout", "userMenu": "User <PERSON>u", "login": "<PERSON><PERSON>", "register": "Sign Up", "verified": "Verified", "underReview": "Under Review", "openMenu": "Open Menu", "dashboard": "Dashboard", "matches": "Matches", "likes": "<PERSON>s"}, "profileImage": {"profilePicture": "Profile Picture", "uploadImage": "Upload Profile Picture", "changeImage": "Change Image", "deleteImage": "Delete Image", "showImage": "Show Image", "hideImage": "Hide Image", "uploadSuccess": "Image uploaded successfully", "uploadError": "Failed to upload image", "deleteSuccess": "Image deleted successfully", "deleteError": "Failed to delete image", "visibilityEnabled": "Image visibility enabled", "visibilityDisabled": "Image visibility disabled", "visibilityError": "Failed to update privacy settings", "supportedFormats": "Supported formats: JPEG, PNG, WebP", "maxSize": "Maximum size: 5MB"}, "shareProfile": {"shareButton": "Share Profile", "modalTitle": "Share My Profile", "profileLink": "Profile Link", "shareVia": "Share via:", "nativeShare": "Share", "whatsapp": "WhatsApp", "email": "Email", "copyLink": "Copy Link", "copied": "Copied!", "copySuccess": "<PERSON>d", "copySuccessDesc": "Your profile link has been copied successfully", "copyError": "<PERSON><PERSON> Failed", "copyErrorDesc": "An error occurred while copying the link", "whatsappMessage": "Hello! You can view my profile on Rizqi website:", "emailSubject": "{{name}}'s Profile on Rizqi", "emailBody": "Hello!\n\nYou can view my profile on Rizqi Islamic matrimonial website:", "shareTitle": "{{name}}'s Profile", "shareText": "View {{name}}'s profile on Rizqi", "privacyNote": "Anyone with this link will be able to view your public profile only"}, "publicProfile": {"loading": {"message": "Loading profile...", "title": "Loading Profile"}, "error": {"notFound": "Profile not found or not available for viewing", "doesNotExist": "Profile does not exist", "loadingError": "An error occurred while loading the profile", "unavailable": "Profile Unavailable", "profilePrivate": "This profile is private and cannot be viewed publicly", "authRequired": "You must be logged in to view profiles", "verificationRequired": "This profile is only available to verified members", "invalidStatus": "Your account is inactive or invalid", "sameGenderNotAllowed": "Access to profiles of the same gender is not allowed for Islamic guidelines", "backButton": "Back"}, "header": {"backButton": "Back", "sendLike": "Send Like", "likeSent": "<PERSON> <PERSON>", "sendMessage": "Send Message", "sending": "Sending..."}, "sections": {"bio": "Personal Bio", "lookingFor": "What I'm Looking For", "basicInfo": "Basic Information", "religiousInfo": "Religious Commitment", "educationWork": "Education & Work", "financialHealth": "Financial & Health Status"}, "fields": {"maritalStatus": "Marital Status", "marriageType": "Marriage Type", "childrenCount": "Number of Children", "nationality": "Nationality", "residenceLocation": "Residence Location", "commitmentLevel": "Commitment Level", "religiosityLevel": "Religiosity Level", "prayerCommitment": "Prayer Commitment", "smoking": "Smoking", "beard": "<PERSON>", "hijab": "Hijab", "education": "Education", "educationLevel": "Education Level", "profession": "Profession", "workField": "Work Field", "jobTitle": "Job Title", "financialStatus": "Financial Status", "monthlyIncome": "Monthly Income", "healthStatus": "Health Status"}, "values": {"yearsOld": "years old", "male": "Male", "female": "Female", "memberSince": "Member since", "notSpecified": "Not specified", "yes": "Yes", "no": "No", "maritalStatus": {"single": "Single", "married": "Married", "divorced": "Divor<PERSON> (Male)", "widowed": "Widowed (Male)", "unmarried": "Unmarried", "divorced_female": "Divorced (Female)", "widowed_female": "Widowed (Female)"}, "marriageType": {"first_wife": "First Wife", "second_wife": "Second Wife", "only_wife": "Only Wife", "no_objection_polygamy": "No Objection to Polygamy"}, "religiousCommitment": {"high": "Very Committed", "medium": "Committed", "practicing": "Practicing"}, "religiosityLevel": {"not_religious": "Not Religious", "slightly_religious": "Slightly Religious", "religious": "Religious", "very_religious": "Very Religious", "prefer_not_say": "Prefer Not to Say"}, "prayerCommitment": {"dont_pray": "Doesn't Pray", "pray_all": "Prays All Prayers", "pray_sometimes": "Prays Sometimes", "prefer_not_say": "Prefer Not to Say"}, "educationLevel": {"primary": "Primary", "secondary": "Secondary", "diploma": "Diploma", "bachelor": "Bachelor's", "master": "Master's", "phd": "PhD"}, "financialStatus": {"poor": "Poor", "below_average": "Below Average", "average": "Average", "above_average": "Above Average", "wealthy": "Wealthy"}, "monthlyIncome": {"less_3000": "Less than 3000", "3000_5000": "3000 - 5000", "5000_8000": "5000 - 8000", "8000_12000": "8000 - 12000", "12000_20000": "12000 - 20000", "more_20000": "More than 20000", "prefer_not_say": "Prefer Not to Say"}, "healthStatus": {"excellent": "Excellent", "very_good": "Very Good", "good": "Good", "fair": "Fair", "poor": "Poor", "prefer_not_say": "Prefer Not to Say"}, "hijab": {"no_hijab": "No Hijab", "hijab": "Hijab", "niqab": "<PERSON><PERSON><PERSON>", "prefer_not_say": "Prefer Not to Say"}}}, "header": {"brand": {"name": "<PERSON><PERSON><PERSON>"}}, "auth": {"loginTitle": "<PERSON><PERSON>", "registerTitle": "Create New Account", "forgotPassword": {"title": "Forgot Password", "subtitle": "Enter your email to receive a temporary password", "description": "We'll send you a temporary password that you can use to set a new password for your account.", "email": "Email Address", "emailPlaceholder": "Enter your email address", "sendButton": "Send Temporary Password", "sendingButton": "Sending...", "backToLogin": "Back to Login", "validation": {"emailRequired": "Please enter your email address", "emailInvalid": "Please enter a valid email address"}, "messages": {"success": "Temporary password sent successfully!", "successDescription": "A temporary password has been sent to your email. It will expire in 60 minutes.", "useNowLink": "Use temporary password now", "emailSendError": "Temporary password was created but failed to send email. Please try again.", "rateLimitError": "Too many requests. Please wait before trying again.", "unexpectedError": "An unexpected error occurred. Please try again later.", "blockedMessage": "Too many failed attempts. Please try again later.", "createPasswordError": "An error occurred while creating temporary password", "warning": "Warning"}}, "rememberMe": "Remember Me", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "login": {"title": "<PERSON><PERSON>", "subtitle": "Welcome back to Rezge", "email": "Email", "password": "Password", "emailPlaceholder": "<EMAIL>", "passwordPlaceholder": "Enter your password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "loginButton": "<PERSON><PERSON>", "loginButtonLoading": "Logging in...", "loginBlocked": "<PERSON><PERSON> temporarily blocked", "attemptsRemaining": "Attempts remaining: {{count}}", "noAccount": "Don't have an account?", "createAccount": "Create new account", "orDivider": "or", "googleLogin": "Login with Google", "facebookLogin": "Login with Facebook", "backToHome": "Back to homepage", "secure": "Secure", "verified": "Verified", "dataProtection": {"title": "Data Protection", "description": "All your data is protected with SSL encryption and adheres to the highest security and privacy standards"}, "validation": {"emailInvalid": "Invalid email address", "passwordRequired": "Password is required", "emailRequired": "Please enter your email first"}, "messages": {"loginSuccess": "Login successful!", "loginError": "Invalid email or password", "unexpectedError": "An unexpected error occurred. Please try again", "resetPasswordSent": "Password reset link has been sent to your email", "resetPasswordError": "An error occurred while sending the reset link", "invalidCredentials": "Invalid email or password", "userNotFound": "User not found", "emailNotConfirmed": "Please confirm your email first", "accountDisabled": "Account is disabled, please contact support", "tooManyAttempts": "Too many attempts, please try again later", "networkError": "Network error, please check your internet connection", "serverError": "Server error, please try again later", "sessionExpired": "Session expired, please login again", "accessDenied": "Access denied", "invalidToken": "Invalid or expired verification token"}}, "twoFactor": {"title": "Two-Factor Authentication", "loginTitle": "Identity Verification", "enableTitle": "Enable Two-Factor Authentication", "disableTitle": "Disable Two-Factor Authentication", "description": "Enter the verification code sent to your email", "enableDescription": "Enter the verification code to enable two-factor authentication", "disableDescription": "Enter the verification code to disable two-factor authentication", "codeSentTo": "Code sent to:", "enterCode": "Enter verification code", "codeLength": "Please enter the complete verification code", "verifyButton": "Verify Code", "verifyingButton": "Verifying...", "resendCode": "Resend Code", "resendingButton": "Sending...", "canResendIn": "You can resend in", "seconds": "seconds", "minutes": "minutes", "hours": "hours", "successMessage": "Verification successful! Redirecting...", "enabledSuccess": "Two-factor authentication enabled successfully", "disabledSuccess": "Two-factor authentication disabled successfully", "invalidCode": "Invalid verification code", "expiredCode": "Verification code has expired", "tooManyAttempts": "Too many attempts. Please try again later", "unexpectedError": "An unexpected error occurred. Please try again.", "backToLogin": "Back to Login", "backToSecurity": "Back to Security Settings", "didntReceiveCode": "Didn't receive the code?", "newCodeSent": "New code sent to your email"}, "resetPassword": {"title": "Reset Password", "description": "Enter your new password for your account", "password": "New Password", "confirmPassword": "Confirm Password", "passwordPlaceholder": "Enter your new password", "confirmPasswordPlaceholder": "Re-enter your password", "updateButton": "Update Password", "updatingButton": "Updating...", "backToLogin": "Back to Login", "validation": {"passwordMinLength": "Password must be at least 8 characters long", "passwordComplexity": "Password must contain uppercase, lowercase and number", "passwordsNotMatch": "Passwords do not match"}, "messages": {"success": "Password updated successfully!", "successDescription": "You will be redirected to the login page...", "invalidSession": "Password reset link is invalid or expired", "updateError": "Failed to update password. Please try again.", "unexpectedError": "An unexpected error occurred. Please try again.", "checkingSession": "Checking link validity...", "invalidLink": "Invalid Link", "requestNewLink": "Request New Link"}}, "createAccount": "Create Account", "confirmPassword": "Confirm Password", "temporaryPassword": {"title": "Set New Password", "subtitle": "Use your temporary password to set a new password", "description": "Enter the temporary password you received via email and create a new secure password.", "email": "Email Address", "emailPlaceholder": "Enter your email address", "temporaryPassword": "Temporary Password", "temporaryPasswordPlaceholder": "Enter the temporary password from email", "newPassword": "New Password", "newPasswordPlaceholder": "Enter your new password", "confirmPassword": "Confirm New Password", "confirmPasswordPlaceholder": "Confirm your new password", "updateButton": "Update Password", "updatingButton": "Updating...", "backToLogin": "Back to Login", "validation": {"emailRequired": "Please enter your email address", "emailInvalid": "Please enter a valid email address", "temporaryPasswordRequired": "Please enter the temporary password", "newPasswordRequired": "Please enter a new password", "newPasswordMinLength": "Password must be at least 8 characters", "newPasswordPattern": "Password must contain uppercase, lowercase letters and numbers", "confirmPasswordRequired": "Please confirm your new password", "passwordsNotMatch": "Passwords do not match"}, "messages": {"success": "Password updated successfully! Logging in...", "updateError": "Failed to update password", "loginError": "Password updated but login failed. Please login manually.", "loginErrorRedirect": "Password updated. Please login manually.", "invalidTempPassword": "Invalid or expired temporary password", "unexpectedError": "An unexpected error occurred. Please try again."}, "passwordStrength": {"weak": "Weak", "fair": "Fair", "good": "Good", "strong": "Strong"}}, "register": {"title": "Create New Account", "subtitle": "Join thousands of Muslims seeking Islamic marriage", "personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone Number", "age": "Age", "city": "City", "gender": "Gender", "maritalStatus": "Marital Status", "firstNamePlaceholder": "Enter your first name", "lastNamePlaceholder": "Enter your last name", "emailPlaceholder": "<EMAIL>", "phonePlaceholder": "Phone Number", "agePlaceholder": "25", "cityPlaceholder": "Riyadh", "genderOptions": {"placeholder": "Select Gender", "male": "Male", "female": "Female"}, "maritalStatusOptions": {"placeholder": "Select Marital Status", "single": "Single", "divorced": "Divorced", "widowed": "Widowed"}, "terms": {"acceptTerms": "I agree to the", "termsLink": "Terms and Conditions", "termsText": "of the website and commitment to Islamic guidelines", "acceptPrivacy": "I agree to the", "privacyLink": "Privacy Policy", "privacyText": "and personal data protection"}, "submitButton": "Confirm Account", "submitButtonLoading": "Sending verification link...", "alreadyHaveAccount": "Already have an account?", "loginLink": "<PERSON><PERSON>", "features": {"secure": "Safe & Secure", "verified": "Islamically Verified", "free": "Free to Start"}, "verification": {"showStats": "Show verification statistics", "hideStats": "Hide verification statistics"}, "validation": {"firstNameMin": "First name must be at least 2 characters", "lastNameMin": "Last name must be at least 2 characters", "emailInvalid": "Invalid email address", "ageMin": "Age must be at least 18 years", "ageMax": "Age must be less than 80 years", "genderRequired": "Please select gender", "cityMin": "Please enter city", "maritalStatusRequired": "Please select marital status", "acceptTermsRequired": "You must agree to the terms and conditions", "acceptPrivacyRequired": "You must agree to the privacy policy", "phoneRequired": "Please enter phone number", "phoneInvalid": "Please enter a valid phone number", "nationalityRequired": "Nationality is required", "professionRequired": "Profession is required", "bioRequired": "About yourself is required (at least 10 characters)", "bioMax": "About yourself must not exceed 500 characters", "formErrors": "Please check all required fields"}, "messages": {"verificationSent": "Verification link sent to", "linkValid": "Link is valid for 24 hours", "dailyLimitReached": "Daily limit reached", "consecutiveLimitReached": "Consecutive attempts limit reached", "nextAttemptTime": "You can try again at:", "waitTime": "You can request a new link after", "minutes": "minutes", "attempts": "attempts", "dailyAttempts": "attempts today", "consecutiveAttempts": "consecutive attempts", "statistics": "Statistics:", "tip": "Tip: Open Developer Tools (F12) and copy the verification link from the Console tab to continue immediately.", "verificationError": "An error occurred while sending the verification link", "unexpectedError": "An unexpected error occurred. Please try again"}, "requiredInfoTitle": "Required Additional Information", "requiredInfoSubtitle": "This information is required to complete your profile and ensure it appears in search results", "optionalInfoTitle": "Optional Additional Information", "optionalInfoSubtitle": "You can fill this information later from your profile page", "bio": "About Yourself", "bioPlaceholder": "Write a brief description about yourself and your personality...", "education": "Education", "educationPlaceholder": "e.g., Bachelor of Engineering", "profession": "Profession", "professionPlaceholder": "e.g., Software Engineer", "religiousCommitment": "Religious Commitment Level", "religiousCommitmentPlaceholder": "Select commitment level", "religiousCommitmentHigh": "Very Committed", "religiousCommitmentMedium": "Committed", "religiousCommitmentPracticing": "Practicing", "nationality": "Nationality", "nationalityPlaceholder": "Select nationality", "height": "Height (cm)", "heightPlaceholder": "e.g., 170", "weight": "Weight (kg)", "weightPlaceholder": "e.g., 70", "educationLevel": "Education Level", "educationLevelPlaceholder": "Select education level", "educationLevelPrimary": "Primary", "educationLevelSecondary": "Secondary", "educationLevelDiploma": "Diploma", "educationLevelBachelor": "Bachelor's", "educationLevelMaster": "Master's", "educationLevelDoctorate": "Doctorate", "prayerCommitment": "Prayer Commitment", "prayerCommitmentPlaceholder": "Select prayer commitment level", "prayerCommitmentDontPray": "Don't pray", "prayerCommitmentPrayAll": "Pray all obligatory prayers", "prayerCommitmentPraySometimes": "Pray sometimes", "prayerCommitmentPreferNotSay": "Prefer not to say", "smoking": "Smoking", "smokingNever": "Never", "smokingOccasionally": "Occasionally", "smokingRegularly": "Regularly", "beard": "Beard (for men)", "beardPlaceholder": "Select beard status", "beardYes": "Yes", "beardNo": "No", "hijab": "Hijab (for women)", "hijabPlaceholder": "Select hijab status", "hijabNoHijab": "No hijab", "hijabHijab": "Hijab", "hijabNiqab": "<PERSON><PERSON><PERSON>", "hijabPreferNotSay": "Prefer not to say", "personalReligiousInfoTitle": "Personal & Religious Information", "religiosityLevel": "Religiosity Level", "religiosityLevelPlaceholder": "Select religiosity level", "religiosityLevelVeryReligious": "Very Religious", "religiosityLevelReligious": "Religious", "religiosityLevelSomewhatReligious": "Somewhat Religious", "religiosityLevelNotReligious": "Not Religious", "financialStatus": "Financial Status", "financialStatusPlaceholder": "Select financial status", "financialStatusPoor": "Poor", "financialStatusBelowAverage": "Below Average", "financialStatusAverage": "Average", "financialStatusAboveAverage": "Above Average", "financialStatusWealthy": "Wealthy", "genderSpecificInfoTitle": "Gender-Specific Information"}}, "profile": {"title": "Enhanced Profile", "subtitle": "Manage your personal information and preferences in detail", "loading": {"title": "Loading Profile", "subtitle": "Please wait while we load your personal data..."}, "personalInfo": "Personal Information", "religiousInfo": "Religious Information", "preferences": "Preferences", "photos": "Photos", "education": "Education", "occupation": "Occupation", "membershipNumber": "Membership Number", "notSpecified": "Not specified", "professionNotSpecified": "Profession not specified", "cityNotSpecified": "City not specified", "years": "years", "user": "User", "edit": "Edit", "cancel": "Cancel", "save": "Save Changes", "fixData": "Fix", "fixDataTitle": "Fix missing data", "errorDuringFix": "❌ An error occurred during the fix", "choose": "Choose...", "maritalStatusSection": {"title": "Marital Status", "marriageType": "Marriage Type", "maritalStatus": "Marital Status", "age": "Age", "childrenCount": "Number of Children", "marriageTypeOptions": {"male": {"firstWife": "First Wife", "secondWife": "Second Wife"}, "female": {"onlyWife": "Only Wife", "noObjectionPolygamy": "No objection to polygamy"}}, "maritalStatusOptions": {"male": {"single": "Single", "married": "Married", "divorced": "Divorced", "widowed": "Widowed"}, "female": {"unmarried": "Unmarried", "divorcedFemale": "Divorced", "widowedFemale": "Widowed"}}}, "nationalitySection": {"title": "Nationality and Residence", "residenceLocation": "Place of Residence (Country)", "nationality": "Nationality", "city": "City", "cityPlaceholder": "Current city"}, "specificationsSection": {"title": "Specifications", "weight": "Weight (kg)", "height": "Height (cm)", "skinColor": "Skin Color", "bodyType": "Body Type", "skinColorOptions": {"veryFair": "Very Fair", "fair": "Fair", "medium": "Medium", "olive": "<PERSON>", "dark": "Dark"}, "bodyTypeOptions": {"slim": "<PERSON>", "average": "Average Build", "athletic": "Athletic", "heavy": "Heavy Build"}}, "religiousSection": {"title": "Religious Commitment", "religiosity": "Religiosity", "prayer": "Prayer", "smoking": "Smoking", "beard": "<PERSON>", "hijab": "Hijab", "religiosityOptions": {"notReligious": "Not Religious", "slightlyReligious": "Slightly Religious", "religious": "Religious", "veryReligious": "Very Religious", "preferNotSay": "Prefer not to say"}, "prayerOptions": {"dontPray": "Don't pray", "prayAll": "Pray all obligatory prayers", "praySometimes": "Pray sometimes", "preferNotSay": "Prefer not to say"}, "smokingOptions": {"yes": "Yes", "no": "No"}, "beardOptions": {"yes": "Yes", "no": "No"}, "hijabOptions": {"noHijab": "No hijab", "hijab": "Hijab", "niqab": "<PERSON><PERSON><PERSON>", "preferNotSay": "Prefer not to say"}}, "educationWorkSection": {"title": "Education and Work", "educationLevel": "Education Level", "financialStatus": "Financial Status", "workField": "Work Field", "jobTitle": "Job Title", "workFieldPlaceholder": "e.g., Information Technology, Medicine, Education", "jobTitlePlaceholder": "e.g., Software Developer, Doctor, Teacher", "educationLevelOptions": {"primary": "Primary", "secondary": "Secondary", "diploma": "Diploma", "bachelor": "Bachelor's", "master": "Master's", "phd": "PhD"}, "financialStatusOptions": {"poor": "Poor", "belowAverage": "Below Average", "average": "Average", "aboveAverage": "Above Average", "wealthy": "Wealthy"}}, "incomeHealthSection": {"title": "Monthly Income and Health Status", "monthlyIncome": "Monthly Income", "healthStatus": "Health Status", "monthlyIncomeOptions": {"less3000": "Less than 3000 SAR", "3000to5000": "3000 - 5000 SAR", "5000to8000": "5000 - 8000 SAR", "8000to12000": "8000 - 12000 SAR", "12000to20000": "12000 - 20000 SAR", "more20000": "More than 20000 SAR", "preferNotSay": "Prefer not to say"}, "healthStatusOptions": {"excellent": "Excellent", "veryGood": "Very Good", "good": "Good", "fair": "Fair", "poor": "Poor", "preferNotSay": "Prefer not to say"}}, "bioSection": {"title": "Personal Bio", "aboutYou": "About You", "lookingFor": "What you're looking for in a life partner", "aboutYouPlaceholder": "Write a brief description of your personality and values...", "lookingForPlaceholder": "Write the qualities you're looking for in a life partner..."}, "validation": {"ageMin": "Age must be at least 18 years", "ageMax": "Age must be less than 80 years", "bioMax": "Personal bio must be less than 500 characters", "lookingForMax": "What you're looking for must be less than 300 characters", "emailInvalid": "Invalid email address"}}, "search": {"title": "Advanced Search", "subtitle": "Find your ideal life partner using advanced search filters", "searchTitle": "Search for Life Partner", "completeProfilesOnly": "Only complete profiles are shown to ensure quality results", "basicSearch": "Basic Search", "advancedSearch": "Advanced Search", "filters": {"title": "Search Filters", "age": "Age", "ageRange": "Age Range", "from": "From", "to": "To", "fromPlaceholder": "From", "toPlaceholder": "To", "country": "Country", "city": "City", "cityPlaceholder": "Choose city", "education": "Education", "educationPlaceholder": "Educational qualification", "occupation": "Occupation", "profession": "Profession", "professionPlaceholder": "Profession", "maritalStatus": "Marital Status", "maritalStatusOptions": {"any": "All", "single": "Single", "divorced": "Divorced", "widowed": "Widowed"}, "religiousCommitment": "Religious Commitment Level", "religiousCommitmentOptions": {"any": "All", "high": "Very Committed", "medium": "Committed", "practicing": "Practicing"}}, "buttons": {"search": "Search", "searching": "Searching...", "clearFilters": "Clear Filters", "clearFiltersAndSearch": "Clear Filters and Search Again"}, "results": {"title": "Search Results", "count": "Search Results ({{count}})", "activeFilters": "Active Filters:", "sortBy": "Sort by:", "sortOptions": {"newest": "Newest", "age": "Age", "rating": "Rating", "lastSeen": "Last Seen"}, "noResults": "No Results", "noResultsMessage": "We couldn't find any results matching your search criteria", "yearsOld": "years old", "lastSeen": "Last seen:", "sendMessage": "Send Message", "sendLike": "Send Like", "likeSent": "<PERSON> <PERSON>", "viewProfile": "View Profile", "showing": "Showing", "of": "of", "results": "results", "page": "Page"}, "pagination": {"previous": "Previous", "next": "Next"}, "messages": {"authenticationCheck": "Checking authentication...", "loginRequired": "Please log in", "loginRequiredMessage": "You must log in to access the search page", "loginButton": "Log In", "loadingProfile": "Loading profile...", "completeProfile": "Please complete your profile", "completeProfileMessage": "You must specify your gender in your profile to access the search page. Please go to your profile page and specify your gender.", "completeProfileTip": "💡 If you specified your gender during registration, try refreshing your profile first", "refreshProfile": "Refresh Profile", "refreshingProfile": "Refreshing...", "completeProfileButton": "Complete Profile", "refreshPage": "Refresh Page", "searchError": "An error occurred while searching. Please try again.", "profileRefreshSuccess": "Profile updated successfully", "profileRefreshError": "Error updating profile", "conversationError": "An error occurred while creating the conversation", "unexpectedError": "An unexpected error occurred"}, "validation": {"ageMinRequired": "Minimum age must be at least 18 years", "ageMaxRequired": "Maximum age must be less than 80 years"}}, "messages": {"messagesTitle": "Messages", "subtitle": "Communicate with members respectfully and safely", "newMessage": "New Message", "sendMessage": "Send Message", "typeMessage": "Type your message here...", "noMessages": "No messages", "conversation": "Conversation", "conversations": "Conversations", "online": "Online", "offline": "Offline", "lastSeen": "Last seen", "messageGuidelines": "Messaging Guidelines", "respectfulCommunication": "Respectful Communication", "noPersonalInfo": "No Personal Information Sharing", "reportInappropriate": "Report Inappropriate Content", "searchConversations": "Search conversations...", "noConversations": "No conversations", "noConversationsDesc": "Start a new conversation from the search page", "searchMembers": "Search for members", "selectConversation": "Select a conversation", "selectConversationDesc": "Choose a conversation from the list to start messaging", "loadingConversations": "Loading conversations...", "loadingMessages": "Loading messages...", "noMessagesYet": "No messages yet", "startConversation": "Start the conversation by sending a message below", "you": "You: ", "minutesAgo": "minutes ago", "hoursAgo": "hours ago", "member": "Website member", "viewProfile": "View profile", "backToConversations": "Back to conversations", "contentModeration": "All messages are reviewed to ensure compliance with Islamic etiquette", "familyInvolvement": {"title": "Involve Family", "description": "You can invite a family member to participate in this conversation", "emailLabel": "Guardian's email address", "emailPlaceholder": "<EMAIL>", "guidelines": "Family Involvement Guidelines", "guidelinesDesc": "An invitation will be sent to the guardian to review the conversation according to Islamic guidelines. This helps ensure the courtship proceeds respectfully and appropriately.", "cancel": "Cancel", "sendInvitation": "Send Invitation"}, "moreOptions": "More options", "reportUser": "Report User", "blockUser": "Block User", "unblockUser": "Unblock User", "deleteConversation": "Delete Chat", "blockModal": {"title": "Confirm User Block", "message": "Are you sure you want to block {name}?\n\nThis user will be completely blocked.", "confirmButton": "Block User Permanently", "cancelButton": "Cancel"}, "unblockModal": {"title": "Confirm User Unblock", "message": "Are you sure you want to unblock {name}?", "confirmButton": "Unblock", "cancelButton": "Keep Blocked"}, "reportModal": {"title": "Report User", "message": "Please specify the reason for reporting {name}.\n\nThe report will be reviewed by administration within 24 hours.", "placeholder": "Describe the reason for reporting in detail (at least 10 characters)...", "confirmButton": "Send Report", "cancelButton": "Cancel"}, "deleteModal": {"title": "Confirm Conversation Deletion", "message": "The conversation and all messages will be permanently deleted from the system for both parties. This action cannot be undone.", "confirmButton": "Delete Chat", "cancelButton": "Cancel"}, "messageStatus": {"sent": "Message sent", "read": "Message read"}, "errors": {"loadingError": "Loading Error", "loadingConversationsError": "An error occurred while loading conversations. Please try again.", "loadingMessagesError": "Cannot load messages for this conversation. Please try again.", "sendingError": "Message sending failed", "sendingErrorDesc": "Cannot send the message at the moment. Please try again.", "unexpectedError": "Unexpected Error", "unexpectedLoadingError": "An unexpected error occurred while loading conversations. Please refresh the page.", "unexpectedMessagesError": "An unexpected error occurred while loading messages. Please try again.", "unexpectedSendingError": "An unexpected error occurred while sending the message. Please try again.", "noActiveConversation": "Error", "noActiveConversationDesc": "No active conversation or user not logged in", "noUserData": "Error", "noUserDataDesc": "Cannot find user data", "familyInviteError": "Family involvement failed", "familyInviteErrorDesc": "An error occurred while involving family. Please try again.", "familyEmailRequired": "Incomplete data", "familyEmailRequiredDesc": "Please enter a valid email address for family", "blockingError": "User blocking failed", "blockingErrorDesc": "An error occurred while blocking the user. Please try again.", "unblockingError": "User unblocking failed", "unblockingErrorDesc": "An error occurred while unblocking the user. Please try again.", "reportingError": "Report sending failed", "reportingErrorDesc": "An error occurred while sending the report. Please try again.", "reportReasonRequired": "Report reason required", "reportReasonRequiredDesc": "You must write a reason for the report", "reportReasonTooShort": "Report reason too short", "reportReasonTooShortDesc": "Must be at least 10 characters", "reportAlreadySubmitted": "Already reported", "reportInvalidData": "Data error", "reportInvalidDataDesc": "The submitted data is incorrect. Please try again.", "reportSelfError": "Operation error", "reportSelfErrorDesc": "You cannot report yourself.", "deleteError": "Deletion failed", "deleteErrorDesc": "An error occurred while deleting the conversation. Please try again.", "messagesDisabled": "Messages disabled", "messagesDisabledDesc": "This user does not allow messages at the moment", "userNotFound": "User not found", "userNotFoundDesc": "The requested user was not found"}, "success": {"messageSent": "<PERSON><PERSON>", "messageSentDesc": "Your message has been sent successfully", "familyInvited": "Family involved", "familyInvitedDesc": "Family invitation sent successfully. They will be notified via email.", "userBlocked": "User blocked successfully", "userBlockedDesc": "{name} has been blocked successfully. They won't be able to send you messages or see your profile. You can unblock them anytime from the same menu.", "userUnblocked": "User unblocked successfully", "userUnblockedDesc": "{name} has been unblocked successfully. You can now communicate again.", "reportSent": "Report sent successfully", "reportSentDesc": "Report sent successfully with ID: {reportId}. The report will be reviewed within 24 hours.", "conversationDeleted": "Deleted", "conversationDeletedDesc": "The conversation and all messages have been permanently deleted from the system."}, "info": {"processing": "Processing", "blockingInProgress": "Blocking user, please wait...", "unblockingInProgress": "Unblocking user, please wait...", "reportingInProgress": "Sending report, please wait..."}, "warnings": {"reportAlreadyExists": "Already reported", "reportLimitReached": "You have already reported this user within the last 24 hours"}}, "home": {"welcomeMessage": "Welcome to Rezge Islamic Marriage", "subtitle": "A safe and Sharia-compliant platform to find your life partner", "featuredProfiles": "Featured Profiles", "recentlyJoined": "Recently Joined", "successStories": "Success Stories", "howItWorks": "How It Works", "step1": "Create Your Profile", "step2": "Search for Compatible Partner", "step3": "Communicate Respectfully", "step4": "Begin Your Halal Marriage Journey", "hero": {"title": "Islamic Marriage Website", "subtitle": "First for Muslims", "description": "Find your ideal life partner in a safe and respectful environment that adheres to Islamic values and Sharia guidelines", "stats": {"members": "thousand members", "matches": "million matches", "users": "million users"}, "cta": {"register": "Join Now", "learn": "Learn More", "watch": "Watch Video"}}, "trustIndicators": {"title": "Why Choose <PERSON>?", "verified": "Verified and secure platform", "sharia": "Compliant with Islamic guidelines", "success": "Successfully connected thousands of couples"}, "websitePreview": {"title": "Explore the Website", "subtitle": "Discover all the features that make <PERSON><PERSON><PERSON> the ideal choice for Islamic marriage"}, "howItWorksSection": {"title": "How Does the Website Work?", "subtitle": "Simple and easy steps to find your life partner", "steps": {"register": {"title": "Create Your Profile", "description": "Sign up and create a comprehensive profile that reflects your personality and Islamic values"}, "search": {"title": "Smart Search", "description": "Use our advanced search tools to find compatible partners based on your preferences"}, "communicate": {"title": "Safe Communication", "description": "Start conversations in a monitored and secure environment that adheres to Islamic etiquette"}, "marriage": {"title": "Halal Marriage", "description": "Begin your marriage journey with family involvement and adherence to Islamic traditions"}}}, "successStoriesSection": {"title": "Real Success Stories", "subtitle": "Stories of couples who found their life partners through <PERSON><PERSON>ge", "stats": {"title": "Our Achievements", "marriages": "Successful Marriages", "members": "Active Members", "countries": "Countries", "satisfaction": "Satisfaction Rate", "experience": "Years of Experience"}, "stories": {"story1": {"text": "Thanks to <PERSON><PERSON><PERSON>, I found my life partner in a safe and respectful environment. The website helped us communicate according to Islamic guidelines.", "author": "<PERSON> and <PERSON><PERSON>", "status": "Married for 2 years"}, "story2": {"text": "The search experience was excellent and the results were accurate. We were able to involve our families from the beginning.", "author": "<PERSON> and <PERSON><PERSON>", "status": "Married for 1 year"}, "story3": {"text": "A wonderful platform that respects Islamic values. We felt safe and comfortable throughout the process.", "author": "<PERSON> and <PERSON><PERSON>", "status": "Married for 3 years"}, "story4": {"text": "Exceptional experience! The website provides a safe and respectful environment for meeting. We are very happy with our marriage and thank the <PERSON><PERSON><PERSON> team for this excellent service.", "author": "<PERSON> and <PERSON><PERSON>", "status": "Married for 3 months"}, "story5": {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, I found my suitable life partner through <PERSON><PERSON><PERSON>. The website respects Islamic values and provides a safe environment for halal courtship.", "author": "<PERSON> and <PERSON><PERSON><PERSON>", "status": "Married for 2 months"}}}, "featuresSection": {"title": "What Makes Our Website Special?", "subtitle": "Discover the features that make <PERSON><PERSON><PERSON> the first choice for Muslims seeking marriage", "features": {"detailedProfile": {"title": "Detailed Profile", "description": "Create a comprehensive profile that reflects your personality and Islamic values with the ability to add precise details about your expectations"}, "privacyProtection": {"title": "Complete Privacy Protection", "description": "We guarantee the protection of your personal data with the highest security standards including content monitoring and identity verification"}, "respectfulCommunication": {"title": "Respectful Communication", "description": "Advanced communication system that ensures adherence to Islamic etiquette with the possibility of involving family in conversations"}}}, "securitySection": {"title": "Security & Privacy Are Our Priority", "subtitle": "We are committed to the highest security standards to protect your personal data and ensure a safe and respectful experience", "features": {"advancedEncryption": {"title": "Advanced Encryption", "description": "All data is protected with SSL 256-bit encryption to ensure maximum security"}, "identityVerification": {"title": "Identity Verification", "description": "Strict system for verifying user identities to ensure the authenticity of personal profiles"}, "contentModeration": {"title": "Content Moderation", "description": "Specialized team monitors content around the clock to ensure adherence to Islamic etiquette"}, "completePrivacy": {"title": "Complete Privacy", "description": "Full control over who can see your profile and your private information"}}, "trustBadges": {"shariaCompliant": "Sharia Compliant", "iso27001": "ISO 27001", "gdprCompliant": "GDPR Compliant"}}, "faqSection": {"title": "Frequently Asked Questions", "subtitle": "Answers to the most common questions about <PERSON><PERSON><PERSON>", "questions": {"q1": {"question": "Is the website free?", "answer": "Yes, you can create an account and start searching for free. We also offer paid plans with additional features like unlimited messages and advanced search."}, "q2": {"question": "How do you ensure adherence to Islamic values?", "answer": "We have a specialized team of scholars and Islamic counselors who oversee all aspects of the website. We also continuously monitor content and apply strict guidelines for respectful communication."}, "q3": {"question": "Can families participate in the process?", "answer": "Absolutely! We encourage family involvement in the courtship process. Parents can create accounts for their children or participate in conversations when needed."}, "q4": {"question": "How do you protect my privacy?", "answer": "We use the latest encryption technologies to protect your data. You can control who sees your profile, and we don't share your information with any third party without your consent."}, "q5": {"question": "What are the success rates?", "answer": "More than 2500 successful marriages through <PERSON><PERSON><PERSON>, with a satisfaction rate of up to 98%. We are proud to have helped thousands of people find their suitable life partner."}}}, "blogSection": {"title": "Articles & Tips for Islamic Marriage", "subtitle": "Discover the latest articles and tips from marriage experts and Islamic counselors", "articles": {"article1": {"date": "December 25, 2024", "author": "Dr. <PERSON>", "title": "Islamic Courtship Etiquette: A Comprehensive Guide for Those Seeking Marriage", "excerpt": "Learn about the Sharia guidelines and Islamic etiquette in courtship before marriage and how to choose the right partner...", "readMore": "Read More →", "imageAlt": "Article Image"}, "article2": {"date": "December 20, 2024", "author": "Ms. <PERSON><PERSON>", "title": "The Role of Family in Choosing a Life Partner: Balancing Opinion and Choice", "excerpt": "How families can help in the process of choosing a life partner while respecting children's wishes and guiding them towards the best choice...", "readMore": "Read More →", "imageAlt": "Article Image"}, "article3": {"date": "December 15, 2024", "author": "Dr. <PERSON>", "title": "Digital Security in Marriage Websites: How to Protect Yourself from Scammers", "excerpt": "Important tips for maintaining your safety and privacy when using online marriage websites and avoiding fraud...", "readMore": "Read More →", "imageAlt": "Article Image"}}, "viewAllButton": "View All Articles"}, "pricingSection": {"title": "Choose the Right Plan for You", "subtitle": "Start for free and discover all available features", "plans": {"free": {"name": "Free Plan", "price": "Free", "period": "/month", "features": ["Create personal profile", "Basic search", "5 messages per month", "View basic profiles"], "button": "Start Free"}, "premium": {"name": "Premium Plan", "price": "$13", "period": "/month", "badge": "Most Popular", "features": ["All free plan features", "Unlimited messages", "Advanced search", "See who viewed your profile", "Hide advertisements"], "button": "Choose Premium"}, "vip": {"name": "VIP Plan", "price": "$26", "period": "/month", "features": ["All premium plan features", "Priority in results", "Free marriage consultation", "Dedicated technical support", "Premium identity verification"], "button": "<PERSON>ose <PERSON>"}}}, "ctaSection": {"title": "Start Your Journey to Halal Marriage Today", "subtitle": "Join thousands of Muslims who trusted <PERSON><PERSON><PERSON> to find their life partner", "buttons": {"register": "Create Free Account", "learn": "Learn More"}, "features": ["Free to start", "Safe & secure", "Sharia compliant"]}}, "features": {"title": "Rezge Features", "subtitle": "Discover what makes <PERSON><PERSON><PERSON> the first choice for Muslims seeking halal marriage", "detailedProfile": {"title": "Detailed Profile", "description": "Create a comprehensive profile that reflects your personality and Islamic values with the ability to add precise details about your expectations and what you're looking for in a life partner", "benefits": {"comprehensive": "Add comprehensive personal details", "preferences": "Set preferences and expectations", "religious": "Display religious and cultural information", "updates": "Continuous update capability"}}, "smartMatching": {"title": "Smart Matching System", "description": "Advanced algorithm that finds you the right partner based on compatibility in values, interests, and life goals while considering Sharia guidelines", "benefits": {"algorithm": "Smart matching algorithm", "compatibility": "Analyze compatibility in values", "suggestions": "Personalized suggestions", "updates": "Continuous results updates"}}, "secureMessaging": {"title": "Secure & Monitored Messaging", "description": "Safe internal messaging system with content monitoring to ensure adherence to Islamic etiquette and the possibility of involving family in conversations", "benefits": {"monitoring": "Automatic content monitoring", "family": "Involve family in conversations", "encryption": "Message encryption", "history": "Save conversation history"}}, "privacyProtection": {"title": "Privacy Protection", "description": "Highest levels of security to protect your personal data with advanced encryption and customizable privacy settings according to your needs", "benefits": {"encryption": "Personal data encryption", "settings": "Advanced privacy settings", "access": "Protection from unauthorized access", "backup": "Secure backups"}}, "familyInvolvement": {"title": "Family Involvement", "description": "Ability to involve family in the getting-to-know and communication process to ensure adherence to Islamic traditions and obtain family approval", "benefits": {"invitation": "Invite family to participate", "monitoring": "Family monitoring of conversations", "approval": "Family approval for communication", "reports": "Regular reports to family"}}, "verifiedProfiles": {"title": "Verified Profiles", "description": "Comprehensive profile verification system that ensures information credibility and reduces fake or misleading attempts", "benefits": {"identity": "Personal identity verification", "verification": "Information verification", "badges": "Verification badges", "fake": "Reduce fake accounts"}}, "islamicGuidelines": {"title": "Sharia Guidelines", "description": "All website features are designed according to Islamic Sharia guidelines with prevention of displaying women's photos and adherence to communication etiquette", "benefits": {"photos": "Prevent displaying women's photos", "communication": "Sharia communication guidelines", "review": "Sharia content review", "guidance": "Islamic guidance"}}, "multiLanguage": {"title": "Multi-Language Support", "description": "Interface in Arabic and English with full support for right-to-left (RTL) text direction for Arab users", "benefits": {"interface": "Complete Arabic interface", "rtl": "RTL support", "translation": "Comprehensive translation", "switching": "Easy language switching"}}, "mobileOptimized": {"title": "Mobile Optimized", "description": "Responsive design that works smoothly on all devices from smartphones to computers with an optimal user experience", "benefits": {"responsive": "Responsive design", "app": "Mobile app coming soon", "speed": "Fast loading speed", "experience": "Perfect user experience"}}, "statistics": {"title": "Numbers Speak for Themselves", "subtitle": "Our achievements in serving the Muslim community", "members": {"number": "10,000+", "label": "registered members"}, "marriages": {"number": "500+", "label": "successful marriages"}, "satisfaction": {"number": "99%", "label": "user satisfaction"}, "support": {"number": "24/7", "label": "technical support"}}, "security": {"title": "Security & Privacy", "subtitle": "We take your data security and privacy seriously", "encryption": {"title": "Advanced Encryption", "description": "All your data is protected with advanced AES-256 encryption"}, "monitoring": {"title": "Content Monitoring", "description": "Smart system for content monitoring and ensuring adherence to etiquette"}, "quality": {"title": "High Quality", "description": "Strict standards to ensure quality of personal profiles"}}}, "about": {"title": "About Us", "subtitle": "We are Rezge platform, the leading Islamic website for halal marriage in the Arab world", "vision": {"title": "Our Vision", "description": "To be the world's leading platform for Islamic marriage, connecting the hearts of believers in a legitimate and respectful way"}, "mission": {"title": "Our Mission", "description": "To facilitate the search for a suitable life partner according to Sharia guidelines, while providing a safe and respectful environment"}, "values": {"title": "Our Values", "subtitle": "The values we believe in and work through", "items": ["Adherence to Sharia guidelines in all aspects of service", "Transparency and credibility in dealing with users", "Protection of privacy and personal data", "Promoting Islamic family values", "Continuous development to serve the Muslim community"]}, "team": {"title": "Our Team", "description": "The Rezge team includes a selection of developers, designers, and Sharia consultants who work passionately to develop the best Islamic marriage experience. We are committed to continuous development and listening to the needs of our Muslim community.", "members": {"development": {"role": "Development Team", "description": "Specialized developers in modern technologies"}, "sharia": {"role": "Sharia Consultants", "description": "Scholars and jurists specialized in marriage rulings"}, "design": {"role": "Design Team", "description": "Creative designers who consider Islamic identity"}, "security": {"role": "Security Team", "description": "Information security experts to protect your data"}}}, "commitment": {"title": "Our Commitment", "description": "We pledge to maintain the highest standards of quality, security, and Sharia compliance in all our services. We also commit to continuous development and responding to the needs and aspirations of our users.", "features": {"quality": {"title": "High Quality", "description": "Strict standards in all our services"}, "innovation": {"title": "Continuous Innovation", "description": "Constant development to improve your experience"}, "global": {"title": "Global Service", "description": "We serve Muslims around the world"}}}, "story": {"title": "Our Story", "description": "The idea of <PERSON><PERSON><PERSON> began from a deep belief in the importance of marriage in Islam and the necessity of providing halal and safe means for getting acquainted. We noticed the urgent need for a platform that combines the ease of modern technology with complete adherence to Sharia guidelines, so <PERSON><PERSON><PERSON> was the fruit of this dream."}}, "contact": {"title": "Contact Us", "subtitle": "We're here to help you in your journey to find your life partner", "form": {"title": "Send us a message", "name": "Full Name", "email": "Email Address", "phone": "Phone Number", "subject": "Subject", "message": "Message", "send": "Send Message", "sending": "Sending...", "success": "Your message has been sent successfully! We'll get back to you soon", "error": "An error occurred while sending the message. Please try again", "required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "messageMinLength": "Message must be at least 10 characters long"}, "info": {"title": "Contact Information", "address": {"title": "Address", "value": "Riyadh, Saudi Arabia"}, "phone": {"title": "Phone", "value": "+************", "dir": "ltr"}, "email": {"title": "Email", "value": "<EMAIL>"}, "hours": {"title": "Business Hours", "value": "Sunday - Thursday: 9:00 AM - 6:00 PM"}}, "support": {"title": "Technical Support", "description": "Our technical support team is available to help you with any technical inquiries", "email": "<EMAIL>", "phone": "+************", "phoneDir": "ltr"}, "sales": {"title": "Sales & Subscriptions", "description": "For inquiries about premium plans and special subscriptions", "email": "<EMAIL>", "phone": "+************", "phoneDir": "ltr"}, "legal": {"title": "Legal Affairs", "description": "For legal inquiries and official complaints", "email": "<EMAIL>"}, "social": {"title": "Follow us on social media", "description": "Stay updated with the latest news and updates"}, "faq": {"title": "Frequently Asked Questions", "description": "You might find the answer to your question in our FAQ section", "button": "View FAQ"}, "departments": {"title": "Specialized Support Departments", "subtitle": "Contact the appropriate department for the best service"}, "map": {"title": "Our Location on the Map", "placeholder": "Interactive Map", "location": "Riyadh, Saudi Arabia", "detailedAddress": "Detailed Address", "address": "King Fahd District, Riyadh 12345"}, "quickContact": {"title": "Need Immediate Help?", "subtitle": "Our technical support team is available 24/7 to help you with any inquiry", "callButton": "Call Us Now", "emailButton": "Email Us"}}, "helpCenter": {"title": "Help Center", "subtitle": "We're here to help you every step of the way in finding your life partner", "searchPlaceholder": "Search in help center...", "categories": {"gettingStarted": {"title": "Getting Started", "description": "Learn how to use the platform and create your profile"}, "profile": {"title": "Profile", "description": "Manage and improve your profile to attract the right partner"}, "matching": {"title": "Matching & Search", "description": "How to find the right partner using our search tools"}, "communication": {"title": "Communication", "description": "Guidelines for Islamic communication and safe messaging"}, "safety": {"title": "Safety & Privacy", "description": "Protect your data and ensure a safe experience"}, "islamic": {"title": "Islamic Guidelines", "description": "Islamic guidance for marriage and courtship"}}, "popularTopics": {"title": "Popular Topics", "topics": ["How to create an attractive profile", "Islamic communication guidelines", "How to search for the right partner", "Privacy and security protection", "Involving family in the process", "Verifying information accuracy"]}, "quickActions": {"title": "Quick Actions", "contact": "Contact Support", "faq": "FAQ", "guidelines": "Islamic Guidelines", "report": "Report an Issue"}, "support": {"title": "Need Additional Help?", "description": "Our support team is available to help you 24/7", "chat": "Live Chat", "email": "Send Email", "phone": "Phone Call"}, "categoriesTitle": "Help Categories", "articlesCount": "articles"}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Answers to the most common questions about using Rezge", "searchPlaceholder": "Search in FAQ...", "categoriesTitle": "Categories", "noResults": {"title": "No results found", "description": "Try searching with different keywords or select another category"}, "categories": {"all": "All Categories", "general": "General", "account": "Account", "profile": "Profile", "matching": "Matching", "communication": "Communication", "safety": "Safety", "islamic": "Islamic Guidelines", "billing": "Billing"}, "questions": [{"id": "what-is-rezge", "category": "general", "question": "What is <PERSON><PERSON><PERSON>?", "answer": "Rezge is an Islamic marriage platform that aims to help Muslims find suitable life partners according to Islamic guidelines. We provide a safe and monitored environment for Islamic courtship."}, {"id": "how-to-register", "category": "account", "question": "How can I create a new account?", "answer": "You can create a new account by clicking the 'Sign Up' button on the homepage, then filling out the form with required information such as name, email, and password. You'll also need to agree to the Terms and Conditions."}, {"id": "forgot-password", "category": "account", "question": "I forgot my password, how can I recover it?", "answer": "You can recover your password by clicking the 'Forgot Password' link on the login page. You'll receive an email with a link to reset your password. Make sure to check your spam folder if you don't see the email."}, {"id": "account-deletion", "category": "account", "question": "How can I delete my account?", "answer": "You can delete your account by going to Account Settings and selecting 'Delete Account'. All your data will be permanently deleted within 30 days. You can reverse this decision by logging in again during this period."}, {"id": "profile-verification", "category": "profile", "question": "How is profile verification done?", "answer": "We verify profiles by reviewing the provided information and ensuring its accuracy. We may request additional documents for identity verification in some cases. Verified profiles receive a blue verification badge."}, {"id": "profile-completion", "category": "profile", "question": "Why should I complete my profile?", "answer": "Completing your profile increases your chances of finding a suitable partner. Complete profiles appear better in search results and get more views. Complete information also helps with our smart matching algorithm."}, {"id": "photo-guidelines", "category": "profile", "question": "What are the photo guidelines for profiles?", "answer": "According to Islamic guidelines, women's photos are not displayed. Men can add modest and appropriate photos. All photos are reviewed before publication. Inappropriate or non-Islamic photos are prohibited."}, {"id": "matching-algorithm", "category": "matching", "question": "How does the matching algorithm work?", "answer": "Our matching algorithm considers multiple factors such as age, education level, location, personal preferences, and shared values. The more detailed your profile, the more accurate the matches will be."}, {"id": "search-filters", "category": "matching", "question": "How can I use advanced search filters?", "answer": "You can access advanced search filters from the search page. You can specify age, education level, profession, location, marital status, and other preferences. Save your favorite filters for later use."}, {"id": "communication-rules", "category": "communication", "question": "What are the communication rules on the platform?", "answer": "Communication on the platform is monitored to ensure adherence to Islamic etiquette. Inappropriate language or content that violates Islamic principles is prohibited. Family involvement in conversations is possible and encouraged, especially in advanced stages of courtship."}, {"id": "message-limits", "category": "communication", "question": "Is there a limit on the number of messages?", "answer": "Free members can send 5 messages per day. Premium members get unlimited messages. You can upgrade your account to get more features and unlimited messaging."}, {"id": "family-involvement", "category": "communication", "question": "How can I involve family in conversations?", "answer": "You can invite a family member to join the conversation through the conversation settings. They will receive an email invitation. This feature helps maintain the Islamic nature of courtship."}, {"id": "privacy-protection", "category": "safety", "question": "How do you protect my privacy and data?", "answer": "We take privacy protection seriously. All data is encrypted and protected, and we don't share your personal information with third parties. You can control the privacy level of your profile and contact information."}, {"id": "report-user", "category": "safety", "question": "How can I report a suspicious user?", "answer": "You can report any user by clicking the 'Report' button on their profile or in the conversation. Our team will review the report within 24 hours and take appropriate action. Your privacy is protected when reporting."}, {"id": "block-user", "category": "safety", "question": "How can I block a specific user?", "answer": "You can block any user by going to their profile and clicking 'Block User'. The blocked user won't be able to see your profile or send you messages. You can unblock them at any time."}, {"id": "islamic-guidelines", "category": "islamic", "question": "What Islamic guidelines are followed on the platform?", "answer": "We adhere to Islamic guidelines in all aspects of our service, including: preventing display of women's photos, monitoring content and messages, enabling family involvement in conversations, and maintaining Islamic courtship etiquette."}, {"id": "islamic-consultation", "category": "islamic", "question": "Can I get Islamic consultation?", "answer": "Yes, we have a team of Islamic consultants specialized in marriage and courtship matters. You can request consultation through the 'Islamic Consultations' section on the website. Consultations are free for all members."}, {"id": "subscription-plans", "category": "billing", "question": "What subscription plans are available?", "answer": "We offer a basic free plan and paid plans (monthly, quarterly, annual) with additional features like unlimited messages, advanced search, and ad-free experience. You can compare plans on the 'Subscriptions' page."}, {"id": "payment-methods", "category": "billing", "question": "What payment methods are accepted?", "answer": "We accept all major credit cards (Visa, Mastercard, American Express), bank transfers, and digital payment methods like Apple Pay and Google Pay. All transactions are secure and encrypted."}, {"id": "refund-policy", "category": "billing", "question": "What is the refund policy?", "answer": "You can request a full refund within 7 days of subscription if you're not satisfied with the service. After this period, partial refunds are possible based on the remaining time. Contact customer service to request a refund."}], "stillNeedHelp": {"title": "Didn't find an answer to your question?", "description": "Our support team is available to help you", "contactButton": "Contact Us"}}, "islamicGuidelines": {"title": "Islamic Guidelines", "subtitle": "Islamic guidance for marriage and courtship according to Islamic teachings", "sections": {"introduction": {"title": "Introduction", "content": "Marriage in Islam is a sunnah of the messengers and a sign of <PERSON>'s creation. It is the only legitimate way to form a Muslim family. At Rezge, we adhere to Islamic guidelines in all aspects of our services to ensure a safe environment that complies with Islamic teachings."}, "courtship": {"title": "Islamic Courtship Etiquette", "guidelines": ["Courtship should be for the purpose of marriage, not for entertainment", "Maintain modesty and dignity in all conversations", "Avoid prohibited privacy and ensure a mahram is present when meeting", "Avoid excessive talking or overly personal details", "Be honest and trustworthy in presenting personal information", "Respect the other party's wish not to continue"]}, "family": {"title": "Role of Family and Guardians", "content": "Family and guardians play an important role in the courtship and marriage process in Islam. We encourage involving family at appropriate stages of courtship and provide the ability to add them to conversations when needed.", "points": ["Consult family in choosing the right partner", "Involve the guardian in advanced stages of courtship", "Respect family opinions and advice", "Maintain transparent communication with family about developments"]}, "privacy": {"title": "Privacy and Modesty", "guidelines": ["No display of women's photos in respect of Islamic guidelines", "Protect personal information and maintain privacy", "Do not share conversation details with others", "Maintain discretion and do not reveal secrets", "Respect the other party's privacy and information"]}, "communication": {"title": "Communication Guidelines", "rules": ["Use respectful and appropriate language in all conversations", "Avoid romantic or overly emotional expressions", "Do not request sensitive personal information initially", "Maintain objectivity and seriousness in conversation", "Avoid controversial or tempting topics"]}, "verification": {"title": "Verification and Credibility", "importance": "Verifying information accuracy is important in Islamic courtship to ensure transparency and honesty.", "steps": ["Verify identity and basic information", "Request references and recommendations when needed", "Confirm marital status and personal circumstances", "Seek help from trusted people for inquiries and verification"]}}, "reminders": {"title": "Important Reminders", "points": ["Pray and seek guidance (<PERSON><PERSON><PERSON><PERSON>) in all stages of courtship", "Be patient and do not rush in making decisions", "Trust in <PERSON> and have confidence in His decree", "Seek <PERSON>'s blessing in this important matter"]}, "callToAction": {"title": "Do you have questions about Islamic guidelines?", "description": "Our Islamic guidance team is available to answer your questions", "faqButton": "Frequently Asked Questions", "contactButton": "Contact Us"}}, "privacyPolicy": {"title": "Privacy Policy", "subtitle": "We respect your privacy and are committed to protecting your personal data", "lastUpdated": "Last updated", "lastUpdatedDate": "December 2024", "tableOfContents": "Table of Contents", "sections": {"introduction": {"title": "Introduction", "content": "At Rezge, we value your privacy and are committed to protecting your personal information. This policy explains how we collect, use, and protect your data when using our services."}, "dataCollection": {"title": "Data We Collect", "intro": "We collect the following types of data:", "types": ["Personal information: Name, age, email, phone number", "Profile information: Education, profession, marital status, preferences", "Usage data: How you interact with the website and services", "Technical information: IP address, browser type, operating system"]}, "dataUsage": {"title": "How We Use Your Data", "intro": "We use the information we collect from you to provide and improve our services, ensuring a safe and effective experience for all users. Here are the main purposes for using your data:", "purposes": ["Provide matching services and help find suitable partners", "Improve user experience and personalize content", "Communicate with you about services and updates", "Ensure security and prevent fraud and unauthorized use", "Comply with legal and regulatory requirements"]}, "dataSharing": {"title": "Data Sharing", "intro": "We do not sell or rent your personal information to third parties. We may share your data in the following cases:", "cases": ["With trusted service providers who help us operate the website", "When we have your explicit consent", "To comply with laws or court orders", "To protect our rights or user safety"]}, "dataSecurity": {"title": "Data Security", "intro": "We take strict security measures to protect your data:", "measures": ["Encrypt sensitive data using latest technologies", "Use secure and protected servers", "Continuous monitoring for suspicious activities", "Train staff on security best practices", "Conduct regular security reviews"]}, "userRights": {"title": "Your Rights", "intro": "You have the following rights regarding your data:", "rights": ["Access and review your personal data", "Correct or update incorrect information", "Delete your account and data (right to be forgotten)", "Restrict processing of your data under certain circumstances", "Transfer your data to another service", "Object to processing your data for marketing purposes"]}, "cookies": {"title": "Cookies", "intro": "We use cookies to improve your experience:", "types": ["Essential cookies: To operate the website properly", "Performance cookies: To analyze website usage", "Functional cookies: To save your preferences", "Marketing cookies: To display personalized ads"], "control": "You can control cookies through your browser settings."}, "contact": {"title": "Contact Us", "intro": "If you have any questions about this privacy policy, you can contact us:", "email": "<EMAIL>", "emailLabel": "Email", "phone": "+************", "phoneLabel": "Phone", "phoneDir": "ltr", "address": "Riyadh, Saudi Arabia", "addressLabel": "Address"}}}, "securitySettings": {"title": "Security & Privacy Settings", "subtitle": "Protect your account and control your privacy", "contactInfo": {"title": "Contact Information", "description": "Update your contact information", "email": "Email Address", "emailPlaceholder": "Enter email address", "phone": "Phone Number", "phonePlaceholder": "Enter phone number", "saveButton": "Save Contact Information", "saving": "Saving...", "notAvailable": "Not Available Currently", "checkingLimits": "Checking request limits...", "pendingChangeTitle": "Contact Information Update Request Pending", "pendingChangeDescription": "A confirmation link for updating contact information has been sent to your current email. The request will expire automatically within 4 hours.", "cancelRequest": "Cancel Request"}, "passwordChange": {"title": "Change Password", "description": "Update your password to ensure account security", "currentPassword": "Current Password", "currentPasswordPlaceholder": "Enter current password", "newPassword": "New Password", "newPasswordPlaceholder": "Enter new password", "confirmPassword": "Confirm New Password", "confirmPasswordPlaceholder": "Re-enter new password", "updateButton": "Update Password", "updating": "Updating...", "strengthLabel": "Password Strength:", "strengthWeak": "Weak", "strengthFair": "Fair", "strengthGood": "Good", "strengthStrong": "Strong", "strengthVeryStrong": "Very Strong"}, "securityPrivacy": {"title": "Security & Privacy Settings", "twoFactor": {"title": "Two-Factor Authentication", "description": "Additional layer of protection for your account"}}, "privacySettings": {"title": "Privacy Settings", "contactVisibility": {"title": "Show Contact Information", "phone": "Phone Number", "email": "Email Address"}, "profileVisibility": {"title": "Who can see your profile", "members": "Registered Members Only", "verified": "Verified Members Only", "private": "Private - No one can see my profile"}, "profileImageVisibility": {"title": "Profile Image Settings", "subtitle": "Control who can see your profile picture", "showImage": "Show Profile Image", "showImageDesc": "Allow verified members to see your picture", "whoCanSee": "Who can see your profile picture:", "verifiedMembers": "Verified Members", "respectsPrivacy": "Respects Privacy", "controlledAccess": "Controlled Access", "importantNote": "Important Note", "noteDescription": "Your profile picture will only be visible to verified members who match your search criteria"}, "communication": {"title": "Communication Settings", "allowMessages": "Allow Messages", "familyCanView": "Allow Family to View"}}, "notifications": {"title": "Notification Settings", "loginNotifications": "Login Notifications", "messageNotifications": "New Message Notifications"}, "securityTips": {"title": "Security Tips", "strongPassword": {"title": "Use a Strong Password", "description": "Choose a password with uppercase, lowercase letters, numbers, and symbols"}, "enableTwoFactor": {"title": "Enable Two-Factor Authentication", "description": "Additional layer of protection that protects your account from hacking"}, "protectInfo": {"title": "Protect Your Personal Information", "description": "Don't share sensitive information with people you don't know"}, "reportSuspicious": {"title": "Report Suspicious Behavior", "description": "Help us maintain community safety by reporting any inappropriate behavior"}}, "buttons": {"reset": "Reset", "saveSettings": "Save Settings", "saving": "Saving..."}, "messages": {"loginRequired": "You must be logged in to access this page", "loginButton": "<PERSON><PERSON>", "contactUpdateSuccess": "Contact information updated successfully", "passwordUpdateSuccess": "Password updated successfully", "twoFactorEnabled": "Two-factor authentication enabled successfully", "twoFactorDisabled": "Two-factor authentication disabled successfully", "settingsUpdateSuccess": "Settings updated successfully", "errorOccurred": "An error occurred. Please try again.", "invalidCurrentPassword": "Current password is incorrect", "passwordMismatch": "Passwords do not match", "weakPassword": "Password is too weak", "emailInvalid": "Invalid email address", "phoneInvalid": "Invalid phone number", "rateLimitExceeded": "Request limit exceeded. Please try again later.", "twoFactorError": "Failed to enable two-factor authentication"}}, "termsOfService": {"title": "Terms of Service", "subtitle": "Terms and conditions governing the use of Rezge website", "lastUpdated": "Last updated", "lastUpdatedDate": "December 2024", "tableOfContents": "Table of Contents", "sections": {"acceptance": {"title": "Acceptance of Terms", "content": "By using Rezge website, you agree to comply with these terms and conditions. If you do not agree to any of these terms, please do not use the website."}, "eligibility": {"title": "Eligibility for Use", "requirements": ["Must be an adult (18 years or older)", "Must be a Muslim committed to Islamic teachings", "Must be unmarried, divorced, or widowed", "Must be serious about seeking marriage", "Must provide accurate and truthful information"]}, "userConduct": {"title": "User Conduct", "intro": "Users must adhere to the following conduct:", "rules": ["Respect Islamic guidelines in all interactions", "Use respectful and appropriate language in conversations", "Do not send inappropriate or immoral content", "Do not harass or disturb other users", "Do not impersonate or provide false information", "Do not use the website for commercial or promotional purposes"]}, "prohibitedActivities": {"title": "Prohibited Activities", "intro": "The following activities are strictly prohibited:", "activities": ["Publishing or sending pornographic or sexual content", "Requesting or exchanging personal photos of women", "Courtship for illegitimate relationships", "Using the website for fraud or scams", "Violating intellectual property rights", "Attempting to hack or disrupt the website"]}, "accountSecurity": {"title": "Account Security", "intro": "You are responsible for maintaining the security of your account:", "responsibilities": ["Choose a strong and secure password", "Do not share login credentials with others", "Report any unauthorized use immediately", "Update your personal information regularly", "Log out from public accounts"]}, "intellectualProperty": {"title": "Intellectual Property", "content": "All content, trademarks, and designs on the website are owned by or licensed to Rezge. Copying, distributing, or using any content without written permission is prohibited."}, "limitation": {"title": "Limitation of Liability", "intro": "We strive to provide the best service, but we do not guarantee:", "disclaimers": ["Success in finding a life partner", "Accuracy of all information provided by users", "Uninterrupted or error-free service", "100% information security", "Compatibility of suggested matches with your expectations"]}, "termination": {"title": "Account Termination", "intro": "Accounts may be terminated in the following cases:", "reasons": ["Violation of terms of use or Islamic guidelines", "Providing false or misleading information", "Misconduct or harassment of users", "Using the website for illegal purposes", "User request to delete their account"]}, "changes": {"title": "Changes to Terms", "content": "We reserve the right to modify these terms at any time. Users will be notified of any significant changes via email or website notification."}, "contact": {"title": "Contact", "intro": "For inquiries about terms of service:", "email": "<EMAIL>", "phone": "+************", "phoneDir": "ltr", "emailLabel": "Email", "phoneLabel": "Phone"}}, "importantNotice": {"title": "Important Notice", "content": "Please read these terms carefully before using the website. Your use of the website means your agreement to all the terms and conditions mentioned above. We are committed to strictly applying these terms to ensure a safe environment that complies with Islamic teachings for all users."}}, "footer": {"brand": {"name": "<PERSON><PERSON><PERSON>", "description": "The first Islamic marriage platform that combines modern technology with authentic Islamic values to help you find your ideal life partner."}, "quickLinks": {"title": "Quick Links", "home": "Home", "features": "Features", "about": "About Us", "search": "Search", "contact": "Contact Us", "help": "Help"}, "support": {"title": "Support & Help", "helpCenter": "Help Center", "faq": "FAQ", "contact": "Contact Us", "islamicGuidelines": "Islamic Guidelines", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service"}, "contactInfo": {"title": "Contact Us", "email": "<EMAIL>", "phone": "+************", "address": "Riyadh, Saudi Arabia"}, "newsletter": {"title": "Subscribe to Newsletter", "placeholder": "Your email address", "subscribe": "Subscribe"}, "copyright": "© 2024 Rezge. All rights reserved."}, "validation": {"emailRequired": "Email is required", "emailInvalid": "<PERSON><PERSON> is invalid", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 8 characters", "passwordsNotMatch": "Passwords do not match", "nameRequired": "Name is required", "phoneRequired": "Phone number is required", "ageRequired": "Age is required", "ageMinimum": "Age must be at least 18 years", "termsRequired": "You must agree to the Terms and Conditions"}}