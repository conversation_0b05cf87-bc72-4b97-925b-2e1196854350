<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أعلام الدول</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .flag-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .flag-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .flag-display {
            font-size: 48px;
            margin: 10px 0;
            line-height: 1;
        }
        .country-name {
            font-weight: bold;
            margin: 10px 0;
            color: #333;
        }
        .flag-code {
            font-family: monospace;
            color: #666;
            font-size: 12px;
        }
        .problem {
            background: #ffe6e6;
            border: 2px solid #ff9999;
        }
    </style>
</head>
<body>
    <h1>اختبار أعلام الدول العربية</h1>
    
    <div class="flag-container">
        <div class="flag-item">
            <div class="country-name">السعودية</div>
            <div class="flag-display">🇸🇦</div>
            <div class="flag-code">🇸🇦 (U+1F1F8 U+1F1E6)</div>
        </div>
        
        <div class="flag-item problem">
            <div class="country-name">الإمارات (الحالي)</div>
            <div class="flag-display">🇦🇪</div>
            <div class="flag-code">🇦🇪 (U+1F1E6 U+1F1EA)</div>
        </div>
        
        <div class="flag-item">
            <div class="country-name">الإمارات (الصحيح)</div>
            <div class="flag-display">🇦🇪</div>
            <div class="flag-code">🇦🇪 (U+1F1E6 U+1F1EA)</div>
        </div>
        
        <div class="flag-item problem">
            <div class="country-name">الكويت (الحالي)</div>
            <div class="flag-display">🇰🇼</div>
            <div class="flag-code">🇰🇼 (U+1F1F0 U+1F1FC)</div>
        </div>
        
        <div class="flag-item">
            <div class="country-name">الكويت (الصحيح)</div>
            <div class="flag-display">🇰🇼</div>
            <div class="flag-code">🇰🇼 (U+1F1F0 U+1F1FC)</div>
        </div>
        
        <div class="flag-item">
            <div class="country-name">مصر</div>
            <div class="flag-display">🇪🇬</div>
            <div class="flag-code">🇪🇬 (U+1F1EA U+1F1EC)</div>
        </div>
        
        <div class="flag-item">
            <div class="country-name">الأردن</div>
            <div class="flag-display">🇯🇴</div>
            <div class="flag-code">🇯🇴 (U+1F1EF U+1F1F4)</div>
        </div>
        
        <div class="flag-item">
            <div class="country-name">قطر</div>
            <div class="flag-display">🇶🇦</div>
            <div class="flag-code">🇶🇦 (U+1F1F6 U+1F1E6)</div>
        </div>
    </div>

    <h2>اختبار بديل - صور الأعلام</h2>
    <div class="flag-container">
        <div class="flag-item">
            <div class="country-name">الإمارات (صورة)</div>
            <div class="flag-display">
                <img src="https://flagcdn.com/w80/ae.png" alt="علم الإمارات" style="width: 48px; height: auto;">
            </div>
            <div class="flag-code">صورة من flagcdn.com</div>
        </div>

        <div class="flag-item">
            <div class="country-name">الكويت (صورة)</div>
            <div class="flag-display">
                <img src="https://flagcdn.com/w80/kw.png" alt="علم الكويت" style="width: 48px; height: auto;">
            </div>
            <div class="flag-code">صورة من flagcdn.com</div>
        </div>

        <div class="flag-item">
            <div class="country-name">السعودية (صورة)</div>
            <div class="flag-display">
                <img src="https://flagcdn.com/w80/sa.png" alt="علم السعودية" style="width: 48px; height: auto;">
            </div>
            <div class="flag-code">صورة من flagcdn.com</div>
        </div>
    </div>

    <script>
        // اختبار دعم الأعلام في المتصفح
        function testFlagSupport() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 20;
            canvas.height = 20;
            
            ctx.font = '16px Arial';
            ctx.fillText('🇦🇪', 0, 16);
            
            const imageData = ctx.getImageData(0, 0, 20, 20);
            const data = imageData.data;
            
            // فحص إذا كان هناك بيكسلات ملونة (يعني العلم يُعرض)
            let hasColor = false;
            for (let i = 0; i < data.length; i += 4) {
                if (data[i] !== 0 || data[i + 1] !== 0 || data[i + 2] !== 0) {
                    hasColor = true;
                    break;
                }
            }
            
            console.log('دعم الأعلام في المتصفح:', hasColor ? 'مدعوم' : 'غير مدعوم');
            
            if (!hasColor) {
                document.body.innerHTML += '<div style="background: #ffeeee; padding: 20px; margin: 20px 0; border-radius: 10px; border: 2px solid #ff6666;"><h3>تحذير: المتصفح لا يدعم عرض أعلام الدول بشكل صحيح</h3><p>قد تحتاج لاستخدام صور بدلاً من رموز Unicode</p></div>';
            }
        }
        
        testFlagSupport();
    </script>
</body>
</html>
