# دليل إعداد نظام الحظر الشامل
## Enhanced Block System Setup Guide

هذا الدليل يوضح كيفية إعداد وتشغيل نظام الحظر الشامل المطور في موقع رزقي.

## 📋 المتطلبات الأساسية

### 1. قاعدة البيانات
يتطلب النظام إنشاء جدول `user_blocks` في قاعدة البيانات Supabase:

```sql
-- تشغيل الملف التالي في Supabase SQL Editor
supabase/migrations/create_user_blocks_table.sql
```

### 2. الملفات المطلوبة
تأكد من وجود الملفات التالية مع التحديثات الجديدة:
- `src/components/MessagesPage.tsx` - صفحة الرسائل المحدثة
- `src/lib/supabase.ts` - خدمات قاعدة البيانات المحدثة
- `src/components/ConfirmModal.tsx` - نوافذ التأكيد

## 🚀 خطوات الإعداد

### الخطوة 1: إعداد قاعدة البيانات
1. افتح Supabase Dashboard
2. انتقل إلى SQL Editor
3. انسخ محتوى ملف `supabase/migrations/create_user_blocks_table.sql`
4. شغل الاستعلام لإنشاء الجدول والدوال المطلوبة

### الخطوة 2: التحقق من الإعداد
```sql
-- تحقق من إنشاء الجدول
SELECT * FROM user_blocks LIMIT 1;

-- تحقق من الدوال
SELECT is_user_blocked('uuid1', 'uuid2');
SELECT * FROM get_blocked_users('uuid1');
```

### الخطوة 3: اختبار النظام
1. افتح ملف `test-enhanced-block-system.html` في المتصفح
2. اتبع خطوات الاختبار المفصلة
3. تأكد من عمل جميع الميزات بشكل صحيح

## 🔧 الميزات المتاحة

### 1. حظر المستخدم
- انقر على قائمة الثلاث نقاط في المحادثة
- اختر "حظر المستخدم"
- اقرأ رسالة التأكيد واضغط "حظر المستخدم نهائياً"

### 2. إلغاء حظر المستخدم
- في المحادثة المحظورة، انقر على قائمة الثلاث نقاط
- اختر "إلغاء حظر المستخدم"
- اقرأ رسالة التأكيد واضغط "إلغاء الحظر"

### 3. فلترة البحث
- المستخدمون المحظورون لا يظهرون في نتائج البحث تلقائياً
- الفلترة تعمل في الاتجاهين (المحظور لا يرى الحاظر والعكس)

## 📊 مراقبة النظام

### استعلامات مفيدة للمراقبة:
```sql
-- عدد المستخدمين المحظورين
SELECT COUNT(*) FROM user_blocks WHERE status = 'active';

-- أكثر المستخدمين حظراً للآخرين
SELECT blocker_id, COUNT(*) as blocks_count 
FROM user_blocks 
WHERE status = 'active' 
GROUP BY blocker_id 
ORDER BY blocks_count DESC;

-- المستخدمون الأكثر تعرضاً للحظر
SELECT blocked_user_id, COUNT(*) as blocked_count 
FROM user_blocks 
WHERE status = 'active' 
GROUP BY blocked_user_id 
ORDER BY blocked_count DESC;
```

## 🛠️ استكشاف الأخطاء

### مشكلة: الجدول غير موجود
```
Error: relation "user_blocks" does not exist
```
**الحل:** تأكد من تشغيل ملف `create_user_blocks_table.sql` في Supabase

### مشكلة: صلاحيات غير كافية
```
Error: permission denied for table user_blocks
```
**الحل:** تحقق من إعدادات RLS في Supabase وتأكد من وجود السياسات الصحيحة

### مشكلة: الخيار لا يتغير
إذا لم يتغير خيار "حظر المستخدم" إلى "إلغاء حظر المستخدم":
1. تحقق من حالة المحادثة في قاعدة البيانات
2. تأكد من تحديث `activeConversation.status` في الكود
3. راجع console للأخطاء

## 📝 ملاحظات مهمة

### الأمان
- جميع العمليات تتطلب مصادقة المستخدم
- RLS مفعل على جدول `user_blocks`
- المستخدم يمكنه فقط إدارة الحظر الذي قام به

### الأداء
- الجدول مفهرس للاستعلامات السريعة
- فلترة البحث محسنة لتجنب التأثير على الأداء
- استخدام دوال قاعدة البيانات للاستعلامات المعقدة

### التوافق
- النظام متوافق مع جميع الميزات الموجودة
- لا يؤثر على المحادثات الموجودة
- يمكن تعطيله بسهولة إذا لزم الأمر

## 🔄 التحديثات المستقبلية

### ميزات مقترحة:
- إحصائيات مفصلة للحظر
- تقارير إدارية للمستخدمين المحظورين
- حظر مؤقت بمدة زمنية محددة
- إشعارات للإدارة عند الحظر المتكرر

### تحسينات تقنية:
- تحسين أداء فلترة البحث
- إضافة cache للاستعلامات المتكررة
- تحسين واجهة المستخدم للأجهزة المحمولة

## 📞 الدعم

إذا واجهت أي مشاكل في الإعداد أو التشغيل:
1. راجع ملف `test-enhanced-block-system.html` للاختبار
2. تحقق من console المتصفح للأخطاء
3. راجع logs قاعدة البيانات في Supabase
4. تأكد من تحديث جميع الملفات المطلوبة

## 📈 مؤشرات النجاح

النظام يعمل بشكل صحيح إذا:
- ✅ يمكن حظر المستخدمين بنجاح
- ✅ يتغير الخيار من "حظر" إلى "إلغاء حظر"
- ✅ المستخدمون المحظورون لا يظهرون في البحث
- ✅ يمكن إلغاء الحظر بنجاح
- ✅ الإشعارات تظهر بشكل صحيح
- ✅ لا توجد أخطاء في console المتصفح

---

**تم تطوير هذا النظام كجزء من مشروع موقع رزقي للزواج الإسلامي الشرعي**
**جميع الميزات تتوافق مع الضوابط الشرعية وتهدف لحماية المستخدمين**
