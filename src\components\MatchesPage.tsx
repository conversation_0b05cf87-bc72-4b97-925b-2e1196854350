import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from './ToastContainer';
import MatchingService, { type MatchResult } from '../lib/matchingService';
import LikesService from '../lib/likesService';
import { profileImageService } from '../lib/profileImageService';
import type { ProfileImage } from '../lib/profileImageService';
import {
  Heart,
  X,
  MapPin,
  GraduationCap,
  Calendar,
  Star,
  MessageCircle,
  Loader2,
  RefreshCw,
  Filter,
  Eye,
  User,
  Info,
  TrendingUp,
  CheckCircle
} from 'lucide-react';

const MatchesPage: React.FC = () => {
  const { i18n } = useTranslation();
  const { userProfile } = useAuth();
  const navigate = useNavigate();
  const { showSuccess, showError, showWarning } = useToast();
  const [matches, setMatches] = useState<MatchResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentMatchIndex, setCurrentMatchIndex] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [likedMatches, setLikedMatches] = useState<string[]>([]);
  const [passedMatches, setPassedMatches] = useState<string[]>([]);
  const [profileImages, setProfileImages] = useState<{ [userId: string]: ProfileImage | null }>({});
  const [isLoadingAction, setIsLoadingAction] = useState(false);

  // فلاتر البحث المتقدمة
  const [filters, setFilters] = useState({
    ageRange: { min: 18, max: 50 },
    cities: [] as string[],
    educationLevels: [] as string[],
    religiousCommitment: [] as string[],
    minCompatibilityScore: 50
  });

  // تحميل المطابقات عند تحميل الصفحة
  useEffect(() => {
    if (userProfile?.id) {
      loadMatches();
    }
  }, [userProfile]);

  // دعم اختصارات لوحة المفاتيح
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (matches.length === 0 || isLoadingAction) return;

      switch (event.key) {
        case ' ': // مسافة للإعجاب
          event.preventDefault();
          handleLike();
          break;
        case 'Escape': // ESC للتمرير
          event.preventDefault();
          handlePass();
          break;
        case 'ArrowRight': // سهم يمين للمطابقة التالية
        case 'ArrowLeft': // سهم يسار للمطابقة التالية
          event.preventDefault();
          nextMatch();
          break;
        case 'Enter': // Enter لعرض الملف الشخصي
          event.preventDefault();
          if (matches[currentMatchIndex]) {
            handleViewProfile(matches[currentMatchIndex].user.id);
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [matches, currentMatchIndex, isLoadingAction]);

  const loadMatches = async () => {
    if (!userProfile?.id) return;

    setIsLoading(true);
    try {
      // تطبيق الفلاتر
      const preferences = {
        ageRange: filters.ageRange,
        educationImportance: 'medium' as const,
        religiousImportance: 'high' as const,
        locationImportance: 'medium' as const
      };

      const { data, error } = await MatchingService.findMatches(userProfile.id, 20, preferences);
      if (error) {
        console.error('Error loading matches:', error);
        showError(
          'خطأ في تحميل المطابقات',
          'حدث خطأ أثناء البحث عن المطابقات. حاول مرة أخرى.',
          5000
        );
      } else {
        // تطبيق الفلاتر المحلية
        const filteredData = applyLocalFilters(data);
        setMatches(filteredData);
        setCurrentMatchIndex(0);

        // تحميل الصور الشخصية للمطابقات
        if (filteredData && filteredData.length > 0) {
          loadProfileImages(filteredData);
        }

        if (filteredData.length === 0) {
          showWarning(
            'لا توجد مطابقات',
            'لم نجد مطابقات مناسبة حالياً. جرب تعديل إعدادات البحث.',
            6000
          );
        } else {
          showSuccess(
            'تم العثور على مطابقات!',
            `وجدنا ${filteredData.length} مطابقة مناسبة لك.`,
            3000
          );
        }
      }
    } catch (error) {
      console.error('Error loading matches:', error);
      showError(
        'خطأ غير متوقع',
        'حدث خطأ غير متوقع. تأكد من اتصالك بالإنترنت وحاول مرة أخرى.',
        5000
      );
    } finally {
      setIsLoading(false);
    }
  };

  // تطبيق الفلاتر المحلية
  const applyLocalFilters = (data: MatchResult[]): MatchResult[] => {
    return data.filter(match => {
      // فلتر نقاط التوافق
      if (match.compatibilityScore < filters.minCompatibilityScore) {
        return false;
      }

      // فلتر المدن
      if (filters.cities.length > 0 && !filters.cities.includes(match.user.city || '')) {
        return false;
      }

      // فلتر مستوى التعليم
      if (filters.educationLevels.length > 0) {
        const userEducation = match.user.education || '';
        const hasMatchingEducation = filters.educationLevels.some(level =>
          userEducation.toLowerCase().includes(level.toLowerCase())
        );
        if (!hasMatchingEducation) {
          return false;
        }
      }

      // فلتر الالتزام الديني
      if (filters.religiousCommitment.length > 0 &&
          !filters.religiousCommitment.includes(match.user.religious_commitment || '')) {
        return false;
      }

      return true;
    });
  };

  // تحميل الصور الشخصية للمطابقات
  const loadProfileImages = async (matchResults: MatchResult[]) => {
    const imagePromises = matchResults.map(async (match) => {
      try {
        const image = await profileImageService.getUserPrimaryImage(match.user.id);
        return { userId: match.user.id, image };
      } catch (error) {
        console.error(`Error loading profile image for user ${match.user.id}:`, error);
        return { userId: match.user.id, image: null };
      }
    });

    const results = await Promise.all(imagePromises);
    const imageMap: { [userId: string]: ProfileImage | null } = {};

    results.forEach((result) => {
      imageMap[result.userId] = result.image;
    });

    setProfileImages(imageMap);
  };

  // الحصول على URL الصورة الشخصية
  const getProfileImageUrl = (userId: string): string | null => {
    const image = profileImages[userId];
    if (image?.file_path) {
      return image.file_path;
    }
    return null;
  };

  // التعامل مع الإعجاب
  const handleLike = async () => {
    const currentMatch = matches[currentMatchIndex];
    if (!currentMatch || !userProfile?.id || isLoadingAction) return;

    setIsLoadingAction(true);
    try {
      // إرسال إعجاب عبر LikesService
      const { success, isMatch } = await LikesService.sendLike(
        userProfile.id,
        currentMatch.user.id,
        'like'
      );

      if (success) {
        // إضافة إلى قائمة الإعجابات
        setLikedMatches(prev => [...prev, currentMatch.user.id]);

        // حفظ المطابقة في قاعدة البيانات
        const saveResult = await MatchingService.saveMatch(
          userProfile.id,
          currentMatch.user.id,
          currentMatch.compatibilityScore,
          isMatch ? 'mutual_like' : 'suggested'
        );

        if (!saveResult.success) {
          console.warn('Failed to save match:', saveResult.error);
          // لا نعرض خطأ للمستخدم لأن الإعجاب تم إرساله بنجاح
        }

        // عرض رسالة نجاح
        if (isMatch) {
          showSuccess(
            '🎉 مطابقة متبادلة!',
            `تهانينا! لديك مطابقة متبادلة مع ${currentMatch.user.first_name}. يمكنكم الآن بدء المحادثة.`,
            6000
          );
        } else {
          showSuccess(
            '💖 تم الإعجاب!',
            `تم إرسال إعجابك إلى ${currentMatch.user.first_name} بنجاح.`,
            3000
          );
        }
      } else {
        showError(
          'خطأ في الإرسال',
          'حدث خطأ أثناء إرسال الإعجاب. حاول مرة أخرى.',
          4000
        );
      }
    } catch (error) {
      console.error('Error sending like:', error);
      showError(
        'خطأ غير متوقع',
        'حدث خطأ غير متوقع. حاول مرة أخرى.',
        4000
      );
    } finally {
      setIsLoadingAction(false);
    }

    // الانتقال للمطابقة التالية
    nextMatch();
  };

  // التعامل مع التمرير
  const handlePass = () => {
    const currentMatch = matches[currentMatchIndex];
    if (!currentMatch || isLoadingAction) return;

    setPassedMatches(prev => [...prev, currentMatch.user.id]);

    // عرض رسالة تأكيد
    showWarning(
      'تم التمرير',
      `تم تمرير ${currentMatch.user.first_name}. يمكنك العثور على مطابقات أخرى.`,
      2000
    );

    nextMatch();
  };

  // الانتقال للمطابقة التالية
  const nextMatch = () => {
    if (currentMatchIndex < matches.length - 1) {
      setCurrentMatchIndex(prev => prev + 1);
    } else {
      // إعادة تحميل مطابقات جديدة
      loadMatches();
    }
  };

  // الحصول على لون نقاط التوافق
  const getCompatibilityColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  // الحصول على نص مستوى التوافق
  const getCompatibilityText = (score: number) => {
    if (score >= 80) return 'توافق ممتاز';
    if (score >= 60) return 'توافق جيد';
    if (score >= 40) return 'توافق متوسط';
    return 'توافق ضعيف';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-12 h-12 text-primary-600 animate-spin mx-auto mb-4" />
          <p className="text-slate-600">جاري البحث عن المطابقات المناسبة...</p>
        </div>
      </div>
    );
  }

  const handleViewProfile = (userId: string) => {
    navigate(`/profile/${userId}`);
  };

  const currentMatch = matches[currentMatchIndex];

  if (!currentMatch) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <Heart className="w-16 h-16 text-slate-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-slate-800 mb-4">لا توجد مطابقات جديدة</h2>
          <p className="text-slate-600 mb-6">
            لقد راجعت جميع المطابقات المتاحة حالياً. سنقوم بإشعارك عند ظهور مطابقات جديدة.
          </p>
          <button
            onClick={loadMatches}
            className="flex items-center gap-2 px-6 py-3 bg-primary-600 text-white rounded-xl hover:bg-primary-700 transition-colors mx-auto"
          >
            <RefreshCw className="w-5 h-5" />
            البحث مرة أخرى
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Floating Action Buttons */}
      <div className="fixed top-4 right-4 z-20 flex gap-2">
        <button
          onClick={() => setShowFilters(true)}
          className="w-12 h-12 bg-white/90 backdrop-blur-sm hover:bg-white shadow-lg rounded-full flex items-center justify-center transition-all hover:scale-105"
          title="فلترة"
        >
          <Filter className="w-5 h-5 text-slate-700" />
        </button>
        <button
          onClick={loadMatches}
          className="w-12 h-12 bg-primary-600 hover:bg-primary-700 shadow-lg rounded-full flex items-center justify-center transition-all hover:scale-105"
          title="تحديث"
        >
          <RefreshCw className="w-5 h-5 text-white" />
        </button>
      </div>

      {/* Filters Popup */}
      {showFilters && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-slate-800 flex items-center gap-2">
                  <Filter className="w-6 h-6" />
                  فلاتر البحث المتقدمة
                </h3>
                <button
                  onClick={() => setShowFilters(false)}
                  className="w-8 h-8 bg-slate-100 hover:bg-slate-200 rounded-full flex items-center justify-center transition-colors"
                >
                  <X className="w-5 h-5 text-slate-600" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Age Range Filter */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-3">
                    العمر ({filters.ageRange.min} - {filters.ageRange.max} سنة)
                  </label>
                  <div className="space-y-3">
                    <div>
                      <span className="text-xs text-slate-500">الحد الأدنى: {filters.ageRange.min}</span>
                      <input
                        type="range"
                        min="18"
                        max="60"
                        value={filters.ageRange.min}
                        onChange={(e) => setFilters(prev => ({
                          ...prev,
                          ageRange: { ...prev.ageRange, min: parseInt(e.target.value) }
                        }))}
                        className="w-full"
                      />
                    </div>
                    <div>
                      <span className="text-xs text-slate-500">الحد الأقصى: {filters.ageRange.max}</span>
                      <input
                        type="range"
                        min="18"
                        max="60"
                        value={filters.ageRange.max}
                        onChange={(e) => setFilters(prev => ({
                          ...prev,
                          ageRange: { ...prev.ageRange, max: parseInt(e.target.value) }
                        }))}
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>

                {/* Compatibility Score Filter */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-3">
                    الحد الأدنى لنقاط التوافق ({filters.minCompatibilityScore}%)
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={filters.minCompatibilityScore}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      minCompatibilityScore: parseInt(e.target.value)
                    }))}
                    className="w-full"
                  />
                </div>

                {/* Religious Commitment Filter */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-3">
                    الالتزام الديني
                  </label>
                  <div className="grid grid-cols-2 gap-3">
                    {['very_religious', 'religious', 'high', 'medium', 'moderate'].map(level => (
                      <label key={level} className="flex items-center p-3 bg-slate-50 rounded-lg hover:bg-slate-100 transition-colors cursor-pointer">
                        <input
                          type="checkbox"
                          checked={filters.religiousCommitment.includes(level)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFilters(prev => ({
                                ...prev,
                                religiousCommitment: [...prev.religiousCommitment, level]
                              }));
                            } else {
                              setFilters(prev => ({
                                ...prev,
                                religiousCommitment: prev.religiousCommitment.filter(l => l !== level)
                              }));
                            }
                          }}
                          className="mr-2"
                        />
                        <span className="text-sm text-slate-700">
                          {level === 'very_religious' ? 'متدين جداً' :
                           level === 'religious' ? 'متدين' :
                           level === 'high' ? 'عالي' :
                           level === 'medium' ? 'متوسط' : 'معتدل'}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between mt-8 pt-6 border-t border-slate-200">
                <button
                  onClick={() => {
                    setFilters({
                      ageRange: { min: 18, max: 50 },
                      cities: [],
                      educationLevels: [],
                      religiousCommitment: [],
                      minCompatibilityScore: 50
                    });
                  }}
                  className="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors"
                >
                  إعادة تعيين
                </button>
                <button
                  onClick={() => {
                    loadMatches();
                    setShowFilters(false);
                  }}
                  className="px-8 py-3 bg-primary-600 text-white rounded-xl hover:bg-primary-700 transition-colors font-medium"
                >
                  تطبيق الفلاتر
                </button>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* Match Progress & Stats */}
      <div className="max-w-4xl mx-auto px-4 py-4">
        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {/* Progress */}
            <div className="md:col-span-2">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-slate-600">
                  المطابقة {currentMatchIndex + 1} من {matches.length}
                </span>
                <span className="text-sm text-slate-600">
                  {Math.round(((currentMatchIndex + 1) / matches.length) * 100)}%
                </span>
              </div>
              <div className="w-full bg-slate-200 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-primary-600 to-emerald-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${((currentMatchIndex + 1) / matches.length) * 100}%` }}
                />
              </div>
            </div>

            {/* Stats */}
            <div className="flex items-center justify-center gap-4 text-sm">
              <div className="flex items-center gap-1 text-green-600">
                <Heart className="w-4 h-4" />
                <span>{likedMatches.length}</span>
              </div>
              <div className="flex items-center gap-1 text-slate-500">
                <X className="w-4 h-4" />
                <span>{passedMatches.length}</span>
              </div>
              <div className="flex items-center gap-1 text-primary-600">
                <TrendingUp className="w-4 h-4" />
                <span>{matches.length - currentMatchIndex - 1} متبقي</span>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          {(likedMatches.length > 0 || passedMatches.length > 0) && (
            <div className="mt-4 pt-4 border-t border-slate-200">
              <div className="text-center text-sm text-slate-600">
                معدل الإعجاب: {Math.round((likedMatches.length / (likedMatches.length + passedMatches.length)) * 100)}%
                {likedMatches.length >= 3 && (
                  <span className="text-green-600 mr-2">
                    • أداء ممتاز! 🎉
                  </span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Match Card */}
        <div className="max-w-md mx-auto px-4 py-2">
          <div className="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
              {/* Profile Header */}
            <div className="bg-gradient-to-r from-primary-600 to-emerald-600 p-4 text-white text-center">
              <div className="w-20 h-20 rounded-full mx-auto mb-3 overflow-hidden border-3 border-white/30 shadow-lg">
                {getProfileImageUrl(currentMatch.user.id) ? (
                  <img
                    src={getProfileImageUrl(currentMatch.user.id)!}
                    alt={`${currentMatch.user.first_name} ${currentMatch.user.last_name}`}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      // في حالة فشل تحميل الصورة، اعرض الأيقونة الافتراضية
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        parent.className = "w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2";
                        parent.innerHTML = '<svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>';
                      }
                    }}
                  />
                ) : (
                  <div className="w-full h-full bg-white/20 flex items-center justify-center">
                    <User className="w-10 h-10 text-white" />
                  </div>
                )}
              </div>
              <h2 className="text-xl font-bold mb-1">
                {currentMatch.user.first_name} {currentMatch.user.last_name}
              </h2>
              <div className="flex items-center justify-center gap-2 mb-1">
                <Calendar className="w-4 h-4" />
                <span className="text-sm">{currentMatch.user.age} سنة</span>
              </div>

              {/* Compatibility Score */}
              <div className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm ${getCompatibilityColor(currentMatch.compatibilityScore)}`}>
                <Star className="w-4 h-4" />
                <span className="font-bold">{currentMatch.compatibilityScore}%</span>
                <span className="text-sm">{getCompatibilityText(currentMatch.compatibilityScore)}</span>
              </div>
            </div>

            {/* Profile Details */}
            <div className="p-3 space-y-2">
              {/* Basic Info Row */}
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="bg-slate-50 rounded-lg p-2 flex items-center gap-1">
                  <MapPin className="w-4 h-4 text-blue-500" />
                  <span className="text-slate-700 truncate font-medium">{currentMatch.user.city}</span>
                </div>
                <div className="bg-slate-50 rounded-lg p-2 flex items-center gap-1">
                  <GraduationCap className="w-4 h-4 text-green-500" />
                  <span className="text-slate-700 truncate font-medium">{currentMatch.user.education}</span>
                </div>
              </div>

              {/* Bio - Enhanced */}
              {currentMatch.user.bio && (
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-2 border border-blue-100">
                  <div className="flex items-start gap-1 mb-1">
                    <User className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm font-medium text-blue-700">نبذة شخصية:</span>
                  </div>
                  <p className="text-sm text-slate-700 leading-relaxed" style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden'
                  }}>{currentMatch.user.bio}</p>
                </div>
              )}

              {/* Match Reasons - Enhanced */}
              <div>
                <h3 className="font-semibold text-slate-800 mb-1 text-sm flex items-center gap-1">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  أسباب التوافق:
                </h3>
                <div className="flex flex-wrap gap-1">
                  {currentMatch.matchReason.slice(0, 3).map((reason, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gradient-to-r from-primary-100 to-primary-50 text-primary-700 rounded-full text-sm border border-primary-200 shadow-sm"
                    >
                      {reason}
                    </span>
                  ))}
                </div>
              </div>

              {/* Compatibility Breakdown - Enhanced */}
              <div>
                <h3 className="font-semibold text-slate-800 mb-2 text-sm flex items-center gap-1">
                  <TrendingUp className="w-4 h-4" />
                  تفاصيل التوافق:
                </h3>
                <div className="bg-slate-50 rounded-lg p-2 space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-slate-600 text-sm">العمر</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-10 bg-slate-200 rounded-full h-1.5">
                        <div
                          className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                          style={{ width: `${currentMatch.compatibilityFactors.age * 100}%` }}
                        />
                      </div>
                      <span className="text-slate-800 font-medium text-sm min-w-[30px]">{Math.round(currentMatch.compatibilityFactors.age * 100)}%</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-slate-600 text-sm">الموقع</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-10 bg-slate-200 rounded-full h-1.5">
                        <div
                          className="bg-green-500 h-1.5 rounded-full transition-all duration-300"
                          style={{ width: `${currentMatch.compatibilityFactors.location * 100}%` }}
                        />
                      </div>
                      <span className="text-slate-800 font-medium text-sm min-w-[30px]">{Math.round(currentMatch.compatibilityFactors.location * 100)}%</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span className="text-slate-600 text-sm">التعليم</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-10 bg-slate-200 rounded-full h-1.5">
                        <div
                          className="bg-purple-500 h-1.5 rounded-full transition-all duration-300"
                          style={{ width: `${currentMatch.compatibilityFactors.education * 100}%` }}
                        />
                      </div>
                      <span className="text-slate-800 font-medium text-sm min-w-[30px]">{Math.round(currentMatch.compatibilityFactors.education * 100)}%</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                      <span className="text-slate-600 text-sm">الدين</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-10 bg-slate-200 rounded-full h-1.5">
                        <div
                          className="bg-amber-500 h-1.5 rounded-full transition-all duration-300"
                          style={{ width: `${currentMatch.compatibilityFactors.religiousCommitment * 100}%` }}
                        />
                      </div>
                      <span className="text-slate-800 font-medium text-sm min-w-[30px]">{Math.round(currentMatch.compatibilityFactors.religiousCommitment * 100)}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="p-3 pt-2">
              <div className="grid grid-cols-4 gap-3">
                {/* Pass Button */}
                <div className="text-center">
                  <button
                    onClick={handlePass}
                    disabled={isLoadingAction}
                    className="w-12 h-12 bg-white hover:bg-slate-50 disabled:bg-slate-50 disabled:cursor-not-allowed rounded-full flex items-center justify-center transition-all hover:scale-105 disabled:hover:scale-100 shadow-md hover:shadow-lg border border-slate-200 group mx-auto mb-2"
                  >
                    <X className="w-5 h-5 text-slate-500 group-hover:text-slate-700 group-disabled:text-slate-300" />
                  </button>
                  <span className="text-sm text-slate-600 block">تمرير</span>
                </div>

                {/* View Profile Button */}
                <div className="text-center">
                  <button
                    onClick={() => handleViewProfile(currentMatch.user.id)}
                    disabled={isLoadingAction}
                    className="w-12 h-12 bg-white hover:bg-blue-50 disabled:bg-slate-50 disabled:cursor-not-allowed rounded-full flex items-center justify-center transition-all hover:scale-105 disabled:hover:scale-100 shadow-md hover:shadow-lg border border-blue-200 group mx-auto mb-2"
                  >
                    <Eye className="w-5 h-5 text-blue-500 group-hover:text-blue-600 group-disabled:text-slate-300" />
                  </button>
                  <span className="text-sm text-blue-600 block">عرض</span>
                </div>

                {/* Like Button */}
                <div className="text-center">
                  <button
                    onClick={handleLike}
                    disabled={isLoadingAction}
                    className="w-14 h-14 bg-white hover:bg-pink-50 disabled:bg-slate-50 disabled:cursor-not-allowed rounded-full flex items-center justify-center transition-all hover:scale-105 disabled:hover:scale-100 shadow-lg hover:shadow-xl border-2 border-pink-200 hover:border-pink-300 mx-auto mb-2"
                  >
                    {isLoadingAction ? (
                      <Loader2 className="w-6 h-6 text-pink-500 animate-spin" />
                    ) : (
                      <Heart className="w-6 h-6 text-pink-500" />
                    )}
                  </button>
                  <span className="text-sm text-pink-600 font-semibold block">إعجاب</span>
                </div>

                {/* Message Button */}
                <div className="text-center">
                  <button
                    className="w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-md border border-slate-200 mx-auto mb-2 opacity-40 cursor-not-allowed"
                    disabled
                  >
                    <MessageCircle className="w-5 h-5 text-slate-400" />
                  </button>
                  <span className="text-sm text-slate-400 block">رسالة</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tips Section at Bottom */}
      <div className="max-w-md mx-auto px-4 pb-6">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-4 border border-blue-200">
          <div className="text-center">
            <Info className="w-6 h-6 text-blue-600 mx-auto mb-2" />
            <h3 className="font-semibold text-blue-800 mb-3">💡 نصائح للحصول على أفضل النتائج</h3>
            <div className="text-sm text-blue-700 space-y-2 text-right">
              <p>• اقرأ الملف الشخصي بعناية قبل اتخاذ القرار</p>
              <p>• انتبه لنقاط التوافق وأسباب المطابقة</p>
              <p>• استخدم الفلاتر لتخصيص البحث حسب تفضيلاتك</p>
              <p>• كن صادقاً في اختياراتك لتحسين جودة المطابقات</p>
            </div>
            <div className="mt-3 pt-3 border-t border-blue-200">
              <p className="text-sm text-blue-600">
                ⌨️ اختصارات: مسافة = إعجاب | ESC = تمرير | Enter = عرض الملف
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MatchesPage;
