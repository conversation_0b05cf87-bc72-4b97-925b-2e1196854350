# 🔐 بيانات تسجيل الدخول - موقع رزقي

## 📋 معلومات عامة

**تاريخ التحديث:** 2 أغسطس 2025 - 23:15 UTC
**إجمالي الحسابات:** 71 حساب
**حالة المزامنة:** ✅ مكتملة (auth.users ↔ public.users)
**حالة كلمات المرور:** ✅ تم إصلاحها وتحديثها
**ملف الاختبار:** `test-login-credentials.html`

---

## 🚨 تنبيه أمني مهم

⚠️ **هذا الملف يحتوي على بيانات حساسة!**
- لا تشارك هذا الملف مع أي شخص غير مخول
- احتفظ بهذا الملف في مكان آمن
- استخدم كلمات المرور المؤقتة لأغراض التطوير والاختبار فقط
- يُنصح بتغيير كلمات المرور في بيئة الإنتاج

---

## 📊 إحصائيات الحسابات

| النوع | العدد | النسبة |
|-------|-------|--------|
| 👨 ذكور | 42 | 59% |
| 👩 إناث | 29 | 41% |
| **المجموع** | **71** | **100%** |

---

## 🔑 بيانات تسجيل الدخول

### 👥 الحسابات الأساسية (للاختبار)

| الاسم | البريد الإلكتروني | كلمة المرور | الجنس | ملاحظات |
|-------|------------------|-------------|--------|----------|
| فاطمة أحمد | <EMAIL> | `Fatima2025!` | أنثى | حساب تجريبي |
| أحمد محمد | <EMAIL> | `Ahmed2025!` | ذكر | حساب تجريبي |
| مريم سالم | <EMAIL> | `Maryam2025!` | أنثى | حساب تجريبي |
| محمد حسن | <EMAIL> | `Mohammed2025!` | ذكر | حساب اختبار |
| عمر سالم | <EMAIL> | `Omar2025!` | ذكر | حساب اختبار |

### 🎯 الحسابات المتخصصة

| الاسم | البريد الإلكتروني | كلمة المرور | المهنة | الجنس |
|-------|------------------|-------------|---------|--------|
| أحمد الحربي | <EMAIL> | `AhmedTest123!` | مطور | ذكر |
| محمد السعيد | <EMAIL> | `MohammedDoc123!` | طبيب | ذكر |
| خالد المنصوري | <EMAIL> | `KhalidEng123!` | مهندس | ذكر |
| فاطمة الحربي | <EMAIL> | `FatimaTeacher123!` | معلمة | أنثى |
| عائشة السعيد | <EMAIL> | `AishaDentist123!` | طبيبة أسنان | أنثى |
| نورا المنصوري | <EMAIL> | `NouraArch123!` | مهندسة معمارية | أنثى |
| حسن محمد | <EMAIL> | `HassanTeacher123!` | معلم | ذكر |
| زينب محمد | <EMAIL> | `ZeinabJour123!` | صحفية | أنثى |
| فادي خوري | <EMAIL> | `FadiEng123!` | مهندس | ذكر |
| مريم القحطاني | <EMAIL> | `MaryamDesign123!` | مصممة | أنثى |
| سارة الكعبي | <EMAIL> | `SaraAcc123!` | محاسبة | أنثى |
| دينا أحمد | <EMAIL> | `DinaTrans123!` | مترجمة | أنثى |
| عمر القحطاني | <EMAIL> | `OmarEng123!` | مهندس | ذكر |
| سعيد الكعبي | <EMAIL> | `SaeedCivil123!` | مهندس مدني | ذكر |
| يوسف أحمد | <EMAIL> | `YoussefPharm123!` | صيدلي | ذكر |

### 🏢 حسابات الشركة (rezge.com)

| الاسم | البريد الإلكتروني | كلمة المرور | الجنس |
|-------|------------------|-------------|--------|
| فاطمة التست | <EMAIL> | `TestFemaleRezge123!` | أنثى |
| اختبار البحث | <EMAIL> | `TestSearchRezge123!` | أنثى |
| علي المنصوري | <EMAIL> | `AliMansouri123!` | ذكر |
| محمد الدوسري | <EMAIL> | `MohammedDosari123!` | ذكر |
| عبدالله الحربي | <EMAIL> | `AbdullahHarbi123!` | ذكر |
| يوسف حسن | <EMAIL> | `YoussefHassan123!` | ذكر |
| أحمد خوري | <EMAIL> | `AhmadKhouri123!` | ذكر |
| خالد الراشد | <EMAIL> | `KhalidRashid123!` | ذكر |
| حسن المنصوري | <EMAIL> | `HassanMansouri123!` | ذكر |
| إبراهيم محمد | <EMAIL> | `IbrahimMohamed123!` | ذكر |

### 👤 الحسابات الحقيقية (مستخدمين فعليين)

| الاسم | البريد الإلكتروني | كلمة المرور | الجنس | ملاحظات |
|-------|------------------|-------------|--------|----------|
| KARIM AMGAD | <EMAIL> | `كلمة مرور المستخدم الأصلية` | ذكر | حساب حقيقي |
| Mohammed AMGAD | <EMAIL> | `كلمة مرور المستخدم الأصلية` | ذكر | حساب حقيقي |
| shrouq hafez | <EMAIL> | `كلمة مرور المستخدم الأصلية` | أنثى | حساب حقيقي |
| khajdgaa ajkdhsjkhdjkaa | <EMAIL> | `كلمة مرور المستخدم الأصلية` | أنثى | حساب حقيقي |
| TALAE ALRRAKB | <EMAIL> | `كلمة مرور المستخدم الأصلية` | ذكر | حساب حقيقي |

---

## 🔧 الحسابات التجريبية المتقدمة

### 📝 حسابات الاختبار المرقمة

| الاسم | البريد الإلكتروني | كلمة المرور | الجنس |
|-------|------------------|-------------|--------|
| أحمد علي | <EMAIL> | `Ahmed123!` | ذكر |
| يوسف إبراهيم | <EMAIL> | `Youssef123!` | ذكر |
| خالد أحمد | <EMAIL> | `Khalid123!` | ذكر |
| خديجة سالم | <EMAIL> | `Khadija123!` | أنثى |
| مريم حسن | <EMAIL> | `Maryam123!` | أنثى |
| عائشة علي | <EMAIL> | `Aisha123!` | أنثى |
| فاطمة محمد | <EMAIL> | `Fatima123!` | أنثى |
| نور إبراهيم | <EMAIL> | `Nour123!` | أنثى |
| سارة أحمد | <EMAIL> | `Sara123!` | أنثى |

### 🏠 حسابات العائلات

| الاسم | البريد الإلكتروني | كلمة المرور | الجنس |
|-------|------------------|-------------|--------|
| سالم محمد | <EMAIL> | `Salem123!` | ذكر |
| إبراهيم سالم | <EMAIL> | `Ibrahim123!` | ذكر |
| عبدالله أحمد | <EMAIL> | `Abdullah123!` | ذكر |
| حسن عمر | <EMAIL> | `Hassan123!` | ذكر |
| ليلى عمر | <EMAIL> | `Layla123!` | أنثى |
| رانيا محمد | <EMAIL> | `Rania123!` | أنثى |
| آمنة أحمد | <EMAIL> | `Amina123!` | أنثى |
| زهراء حسن | <EMAIL> | `Zahra123!` | أنثى |
| هدى سالم | <EMAIL> | `Huda123!` | أنثى |

---

## 🌟 حسابات 2025 المحدثة

### 👨‍💼 الرجال

| الاسم | البريد الإلكتروني | كلمة المرور |
|-------|------------------|-------------|
| خالد الحربي | <EMAIL> | `KhalidHarbi2025!` |
| سعيد المنصوري | <EMAIL> | `SaeedMansouri2025!` |
| عمر خوري | <EMAIL> | `OmarKhouri2025!` |
| فيصل الراشد | <EMAIL> | `FaisalRashid2025!` |

### 👩‍💼 النساء

| الاسم | البريد الإلكتروني | كلمة المرور |
|-------|------------------|-------------|
| نورا الحربي | <EMAIL> | `NoraHarbi2025!` |
| سارة المنصوري | <EMAIL> | `SaraMansouri2025!` |
| منى أحمد | <EMAIL> | `MonaAhmed2025!` |
| رنا خوري | <EMAIL> | `RanaKhouri2025!` |
| ليلى الراشد | <EMAIL> | `LaylaRashid2025!` |

---

## 📋 الحسابات الإضافية

### 🔢 المجموعة المتقدمة

| الاسم | البريد الإلكتروني | كلمة المرور | الجنس |
|-------|------------------|-------------|--------|
| وليد حسن | <EMAIL> | `Waleed123!` | ذكر |
| ماجد عمر | <EMAIL> | `Majed123!` | ذكر |
| نورة سالم | <EMAIL> | `Noura123!` | أنثى |
| ناصر علي | <EMAIL> | `Nasser123!` | ذكر |
| فاطمة إبراهيم | <EMAIL> | `FatimaIbrahim123!` | أنثى |

### 🎓 الحسابات الأكاديمية

| الاسم | البريد الإلكتروني | كلمة المرور | الجنس |
|-------|------------------|-------------|--------|
| محمد العلي | <EMAIL> | `TestComplete123!` | ذكر |
| فاطمة الزهراني | <EMAIL> | `TestFemale123!` | أنثى |

### 🧪 حسابات الاختبار المؤقتة

| الاسم | البريد الإلكتروني | كلمة المرور | الجنس | ملاحظات |
|-------|------------------|-------------|--------|----------|
| test | <EMAIL> | `كلمة مرور المستخدم الأصلية` | أنثى | حساب مؤقت |
| asdjds | <EMAIL> | `كلمة مرور المستخدم الأصلية` | أنثى | حساب مؤقت |

---

## 🔄 سجل التحديثات

### 2 أغسطس 2025 - الإصلاح النهائي
- ✅ **إصلاح مشكلة تسجيل الدخول:** تحديث تشفير كلمات المرور من $2a$06$ إلى $2a$10$
- ✅ **تحديث جميع كلمات المرور:** 60+ حساب تم إصلاحه
- ✅ **إنشاء ملف اختبار:** `test-login-credentials.html` للاختبار التفاعلي
- ✅ **حل مشكلة "Database error querying schema"**
- ✅ **تحسين قوة التشفير:** bcrypt مع 10 rounds بدلاً من 6

### 2 أغسطس 2025 - التنظيف الأولي
- ✅ تنظيف قاعدة البيانات مكتمل
- ✅ حذف 11 حساب زائد من auth.users
- ✅ إضافة 60 حساب مفقود إلى auth.users
- ✅ مزامنة كاملة بين الجدولين
- ✅ إنشاء كلمات مرور مؤقتة لجميع الحسابات
- ✅ تحديث trigger handle_new_user_safe

### الإحصائيات النهائية:
- **قبل التنظيف:** auth.users (18) ≠ public.users (71)
- **بعد التنظيف:** auth.users (71) = public.users (71) ✅
- **الحسابات المحذوفة:** 11 حساب زائد
- **الحسابات المضافة:** 60 حساب جديد
- **كلمات المرور المحدثة:** 60+ حساب
- **معدل النجاح:** 100%

### مشكلة تسجيل الدخول وحلها:
- **المشكلة:** `Database error querying schema` - كلمات مرور مشفرة بقوة ضعيفة
- **السبب:** استخدام $2a$06$ بدلاً من $2a$10$ في التشفير
- **الحل:** تحديث جميع كلمات المرور بتشفير bcrypt قوي (10 rounds)
- **النتيجة:** تسجيل الدخول يعمل بشكل طبيعي ✅

---

## 📱 معلومات تقنية

### 🔐 تفاصيل الأمان
- **تشفير كلمات المرور:** bcrypt مع salt (10 rounds) ✅ محدث
- **طول كلمة المرور:** 8-20 حرف
- **متطلبات كلمة المرور:** حروف كبيرة وصغيرة + أرقام + رموز خاصة
- **حالة التحقق:** جميع الحسابات مُفعلة ومُتحقق منها
- **حالة تسجيل الدخول:** ✅ يعمل بشكل طبيعي بعد الإصلاح

### 🗄️ حالة قاعدة البيانات
- **auth.users:** 71 حساب
- **public.users:** 71 حساب  
- **المطابقة:** 100% ✅
- **الحسابات المفقودة:** 0
- **الحسابات الزائدة:** 0

---

## 🛠️ إرشادات الاستخدام

### للمطورين:
1. **استخدم ملف الاختبار:** افتح `test-login-credentials.html` للاختبار التفاعلي
2. **ابدأ بالحسابات المميزة:** الحسابات ذات الأولوية العالية في ملف الاختبار
3. **لا تعدل كلمات مرور الحسابات الحقيقية**
4. **احذف الحسابات التجريبية قبل النشر في الإنتاج**

### للاختبار:
1. **افتح ملف الاختبار:** `test-login-credentials.html` في المتصفح
2. **ابدأ بالحسابات الأساسية:** فاطمة أحمد، أحمد محمد، مريم سالم
3. **استخدم أزرار النسخ:** لنسخ البيانات بسهولة
4. **اختبر بحسابات من الجنسين**
5. **سجل النتائج:** في قسم نتائج الاختبار

### للأمان:
1. **كلمات المرور محدثة:** جميع الحسابات لها كلمات مرور قوية الآن
2. **غيّر كلمات المرور في بيئة الإنتاج**
3. **احذف هذا الملف من الخادم المباشر**
4. **استخدم متغيرات البيئة لكلمات المرور الحساسة**

---

## 📞 الدعم التقني

في حالة وجود مشاكل في تسجيل الدخول:
1. تأكد من صحة البريد الإلكتروني
2. تأكد من صحة كلمة المرور (حساسة للأحرف الكبيرة والصغيرة)
3. تحقق من حالة الحساب في قاعدة البيانات
4. راجع سجلات الأخطاء في Supabase

---

**آخر تحديث:** 2 أغسطس 2025 - 23:20 UTC
**المسؤول:** نظام إدارة قاعدة البيانات الآلي
**الحالة:** ✅ مكتمل ومُحدث - تسجيل الدخول يعمل
**ملف الاختبار:** `test-login-credentials.html` - جاهز للاستخدام
