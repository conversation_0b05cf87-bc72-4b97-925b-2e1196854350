<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة الأعلام - Flag Debug</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f8fafc;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background-color: #f8fafc;
        }
        .flag-test {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
        .flag-display {
            font-size: 2rem;
            font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Segoe UI Symbol", sans-serif;
            line-height: 1;
        }
        .flag-info {
            flex: 1;
        }
        .profile-demo {
            position: relative;
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #4299e1 0%, #10b981 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            font-size: 2.5rem;
            color: white;
        }
        .flag-overlay {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 32px;
            height: 32px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            border: 2px solid white;
            font-size: 1.2rem;
            font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Segoe UI Symbol", sans-serif;
        }
        .error { color: #e53e3e; }
        .success { color: #38a169; }
        .warning { color: #d69e2e; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشكلة عرض أعلام الدول</h1>
        
        <div class="test-section">
            <h2>1. اختبار عرض الأعلام المباشر</h2>
            <p>هذا اختبار لعرض أعلام الدول مباشرة في HTML:</p>
            
            <div class="flag-test">
                <div class="flag-display">🇸🇦</div>
                <div class="flag-info">
                    <strong>السعودية:</strong> 🇸🇦 (Unicode: U+1F1F8 U+1F1E6)
                </div>
            </div>
            
            <div class="flag-test">
                <div class="flag-display">🇦🇪</div>
                <div class="flag-info">
                    <strong>الإمارات:</strong> 🇦🇪 (Unicode: U+1F1E6 U+1F1EA)
                </div>
            </div>
            
            <div class="flag-test">
                <div class="flag-display">🇪🇬</div>
                <div class="flag-info">
                    <strong>مصر:</strong> 🇪🇬 (Unicode: U+1F1EA U+1F1EC)
                </div>
            </div>
            
            <div class="flag-test">
                <div class="flag-display">🇯🇴</div>
                <div class="flag-info">
                    <strong>الأردن:</strong> 🇯🇴 (Unicode: U+1F1EF U+1F1F4)
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>2. اختبار في محاكاة البروفايل</h2>
            <p>هكذا يجب أن يظهر العلم في البروفايل:</p>
            
            <div class="profile-demo">
                👤
                <div class="flag-overlay">🇸🇦</div>
            </div>
            <p style="text-align: center;">مثال: مستخدم سعودي</p>
        </div>

        <div class="test-section">
            <h2>3. تشخيص المشكلة</h2>
            
            <div id="browser-support"></div>
            
            <h3>الأسباب المحتملة لعرض "SA" بدلاً من 🇸🇦:</h3>
            <ul>
                <li><span class="error">❌</span> المتصفح لا يدعم emoji الأعلام</li>
                <li><span class="error">❌</span> نظام التشغيل لا يحتوي على خطوط الأعلام</li>
                <li><span class="error">❌</span> مشكلة في ترميز Unicode</li>
                <li><span class="error">❌</span> الكود يعرض country.code بدلاً من country.flag</li>
                <li><span class="error">❌</span> مشكلة في CSS font-family</li>
            </ul>

            <h3>الحلول:</h3>
            <ul>
                <li><span class="success">✅</span> استخدام صور الأعلام من مصدر خارجي</li>
                <li><span class="success">✅</span> إضافة font-family مخصص للأعلام</li>
                <li><span class="success">✅</span> استخدام مكتبة أعلام مخصصة</li>
                <li><span class="success">✅</span> إضافة fallback للمتصفحات القديمة</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>4. اختبار JavaScript</h2>
            <button onclick="testFlags()">اختبار عرض الأعلام</button>
            <div id="js-test-results"></div>
        </div>

        <div class="test-section">
            <h2>5. معلومات المتصفح</h2>
            <div id="browser-info"></div>
        </div>
    </div>

    <script>
        // اختبار دعم المتصفح للأعلام
        function checkBrowserSupport() {
            const testDiv = document.createElement('div');
            testDiv.innerHTML = '🇸🇦';
            testDiv.style.fontFamily = '"Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Segoe UI Symbol", sans-serif';
            document.body.appendChild(testDiv);
            
            const rect = testDiv.getBoundingClientRect();
            const hasWidth = rect.width > 0;
            
            document.body.removeChild(testDiv);
            
            const supportDiv = document.getElementById('browser-support');
            if (hasWidth) {
                supportDiv.innerHTML = '<p class="success">✅ المتصفح يدعم عرض أعلام emoji</p>';
            } else {
                supportDiv.innerHTML = '<p class="error">❌ المتصفح لا يدعم عرض أعلام emoji بشكل صحيح</p>';
            }
        }

        // اختبار عرض الأعلام
        function testFlags() {
            const flags = {
                'السعودية': '🇸🇦',
                'الإمارات': '🇦🇪', 
                'مصر': '🇪🇬',
                'الأردن': '🇯🇴'
            };

            let html = '<h4>نتائج الاختبار:</h4>';
            
            for (const [country, flag] of Object.entries(flags)) {
                html += `
                    <div class="flag-test">
                        <div class="flag-display">${flag}</div>
                        <div class="flag-info">
                            <strong>${country}:</strong> ${flag}
                            <br><small>Unicode: ${[...flag].map(c => 'U+' + c.codePointAt(0).toString(16).toUpperCase()).join(' ')}</small>
                        </div>
                    </div>
                `;
            }

            document.getElementById('js-test-results').innerHTML = html;
        }

        // معلومات المتصفح
        function showBrowserInfo() {
            const info = {
                'User Agent': navigator.userAgent,
                'Platform': navigator.platform,
                'Language': navigator.language,
                'Fonts Available': 'يتم فحصها...'
            };

            let html = '<h4>معلومات المتصفح:</h4>';
            for (const [key, value] of Object.entries(info)) {
                html += `<p><strong>${key}:</strong> ${value}</p>`;
            }

            document.getElementById('browser-info').innerHTML = html;
        }

        // تشغيل الاختبارات عند تحميل الصفحة
        window.onload = function() {
            checkBrowserSupport();
            showBrowserInfo();
        };
    </script>
</body>
</html>
