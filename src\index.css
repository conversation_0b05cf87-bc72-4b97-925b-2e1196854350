@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

[dir="ltr"] {
  direction: ltr;
}

/* Custom Islamic Design Elements */
@layer base {
  html {
    font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
  }

  html[lang="ar"] {
    font-family: 'Cairo', 'Tajawal', sans-serif;
  }

  body {
    background-color: #ffffff;
    color: #1e293b;
    margin: 0;
    padding: 0;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    direction: rtl;
    /* إصلاح مشاكل التخطيط والطول الزائد */
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
  }

  html {
    /* منع التمرير الأفقي غير المرغوب فيه */
    overflow-x: hidden;
    /* ضمان ارتفاع كامل للصفحة */
    height: 100%;
  }

  /* إصلاح مشكلة الطول الزائد في الصفحات */
  #root {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* ضمان أن المحتوى الرئيسي يأخذ المساحة المتبقية */
  main {
    flex: 1;
    min-height: 0; /* يسمح للمحتوى بالتقلص حسب الحاجة */
  }
}

@layer components {
  /* إصلاح مشاكل التمرير والتخطيط */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  .prevent-layout-shift {
    contain: layout style;
  }

  /* إصلاح مشكلة الطول الزائد في الصفحات الديناميكية */
  .dynamic-height-fix {
    min-height: 100vh;
    height: auto;
    overflow: hidden;
  }

  .dynamic-height-fix.loaded {
    overflow: visible;
    height: auto;
    min-height: auto;
  }

  /* Fix for gradient text clipping issues */
  .gradient-text-fix {
    background: linear-gradient(135deg, #6366f1 0%, #10b981 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    padding-bottom: 0.25rem;
    line-height: 1.2;
    display: inline-block;
    position: relative;
  }

  /* Extra loose line height for main title */
  .extra-loose-leading {
    line-height: 1.3;
  }

  /* Islamic Card Design */
  .islamic-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e7e5e4;
    padding: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  }

  /* Islamic Button Styles */
  .btn-primary {
    background: linear-gradient(135deg, #6366f1 0%, #10b981 100%);
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: none;
    cursor: pointer;
    transform: translateY(0);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #4f46e5 0%, #059669 100%);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
  }

  .btn-secondary {
    background-color: white;
    color: #6366f1;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    border: 2px solid #e2e8f0;
    cursor: pointer;
  }

  .btn-secondary:hover {
    border-color: #6366f1;
    background-color: #f8fafc;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* Islamic Form Styles */
  .form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d6d3d1;
    border-radius: 0.5rem;
    transition: border-color 0.2s, box-shadow 0.2s;
  }

  .form-input:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
  }

  /* Islamic Typography */
  .heading-arabic {
    font-family: 'Amiri', serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: #44403c;
  }

  .text-arabic {
    font-family: 'Amiri', serif;
    font-size: 1rem;
    line-height: 1.625;
  }

  /* Islamic Decorative Elements */
  .islamic-pattern {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }
}

@layer utilities {
  /* Floating Animation */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    25% {
      transform: translateY(-10px) rotate(5deg);
    }
    50% {
      transform: translateY(-20px) rotate(0deg);
    }
    75% {
      transform: translateY(-10px) rotate(-5deg);
    }
  }
}
