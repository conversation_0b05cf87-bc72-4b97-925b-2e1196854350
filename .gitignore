# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
dist-ssr/
build/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache/
.parcel-cache/

# Next.js build output
.next/

# Nuxt.js build / generate output
.nuxt/

# Storybook build outputs
.out/
.storybook-out/

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Supabase
.supabase/
supabase/.env
supabase/.env.local

# Database
*.db
*.sqlite
*.sqlite3

# Test files
test-*.html
debug-*.md
console.txt

# Backup files
*.backup
*.bak
*.orig

# Package manager lock files (keep only one)
# Uncomment the one you're NOT using:
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Local development files
.local/
.cache/

# Vercel
.vercel/

# Netlify
.netlify/

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Sentry
.sentryclirc

# TypeScript
*.tsbuildinfo

# ESLint
.eslintcache

# Prettier
.prettierignore

# Tailwind CSS
# (usually you want to keep tailwind.config.js)

# Custom project files
*.md.backup
fix-*.md
test-*.html
SETUP.md.backup
console.txt
debug-*.md
final-*.md
*.rar
*.zip
*.tar.gz

# Project-specific documentation files (keep main README.md)
ABOUT_PAGE_TRANSLATION_README.md
ADVANCED_SECURITY_GUIDE.md
CONTACT_PAGE_TRANSLATION_README.md
EMAIL_SOLUTION_SUMMARY.md
EMAIL_SYSTEM_GUIDE.md
ENHANCED_LOGIN_SECURITY_GUIDE.md
ENHANCED_PROFILE_DEVELOPMENT_README.md
FAQ_PAGE_TRANSLATION_AND_ENHANCEMENT_README.md
HELP_CENTER_TRANSLATION_VERIFICATION_README.md
ISLAMIC_GUIDELINES_TRANSLATION_README.md
LOGIN_PAGE_TRANSLATION_README.md
PRIVACY_POLICY_TRANSLATION_README.md
REGISTER_PAGE_TRANSLATION_README.md
TERMS_OF_SERVICE_TRANSLATION_README.md
VERIFICATION_LIMITS_GUIDE.md

# Test files (all test-*.html files)
test-advanced-security-system.html
test-auth-fix.html
test-create-and-login.html
test-direct-login.html
test-email-system.html
test-enhanced-login-security.html
test-enhanced-profile-simple.html
test-enhanced-profile.html
test-error-messages.html
test-final-email.html
test-fixed-login.html
test-login-errors.html
test-login-redirect-fix.html
test-login-security-debug.html
test-login-system.html
test-phone-number-fix.html
test-profile-fixes.html
test-profile-form-debug.html
test-profile-functionality.html
test-profile-save-fix.html
test-profile-validation-fix.html
test-simple-login-security.html
test-simple-login.html
test-trigger-system.html
test-verification-system.html

# Important files to keep in repository:
# - README.md (main documentation)
# - .env.example (environment variables template)
# - package.json & package-lock.json (dependencies)
# - tsconfig.json & tsconfig.*.json (TypeScript configuration)
# - vite.config.ts (Vite configuration)
# - tailwind.config.js (Tailwind CSS configuration)
# - postcss.config.js (PostCSS configuration)
# - eslint.config.js (ESLint configuration)
# - vercel.json (deployment configuration)
# - src/ directory (source code)
# - public/ directory (static assets)
# - supabase/migrations/ (database migrations)
# - supabase/functions/ (edge functions)
