# دليل تشغيل موقع الزواج الإسلامي - رزقي

## متطلبات النظام
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn
- حساب Supabase (مجاني)

## خطوات التشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd indexxx
```

### 2. تثبيت الحزم
```bash
npm install
```

### 3. إعداد قاعدة البيانات
قاعدة البيانات جاهزة ومُعدة مسبقاً على Supabase:
- **URL:** https://sbtzngewizgeqzfbhfjy.supabase.co
- **المفتاح العام:** متوفر في ملف `.env.local`

### 4. تشغيل المشروع
```bash
npm run dev
```

سيعمل المشروع على: http://localhost:5173

## الصفحات المتاحة

### للمستخدمين العاديين:
- `/` - الصفحة الرئيسية
- `/register` - التسجيل
- `/login` - تسجيل الدخول
- `/profile` - الملف الشخصي
- `/search` - البحث المتقدم
- `/messages` - المراسلات
- `/security` - إعدادات الأمان

### للمشرفين:
- `/admin` - لوحة تحكم المشرفين

## البيانات التجريبية

### حسابات للاختبار:
1. **أحمد محمد**
   - البريد: <EMAIL>
   - كلمة المرور: (يجب إنشاء حساب جديد)

2. **فاطمة أحمد**
   - البريد: <EMAIL>
   - كلمة المرور: (يجب إنشاء حساب جديد)

3. **مريم سالم**
   - البريد: <EMAIL>
   - كلمة المرور: (يجب إنشاء حساب جديد)

## الميزات المطبقة

### ✅ الميزات الأساسية:
- نظام تسجيل/دخول آمن مع Supabase Auth
- ملفات تعريف شاملة (بدون صور للنساء)
- محرك بحث متقدم مع فلاتر
- نظام مراسلات مع مراقبة المحتوى
- لوحة تحكم إدارية كاملة
- نظام أمان وحماية متقدم

### ✅ الالتزام بالضوابط الشرعية:
- منع عرض صور النساء
- مراقبة جميع الرسائل والمحتوى
- إمكانية إشراك الأهل في التواصل
- ضوابط التواصل الشرعي
- واجهة عربية بالكامل

### ✅ التقنيات المستخدمة:
- React 18 + TypeScript + Vite
- Tailwind CSS مع دعم RTL
- Supabase للبيانات والمصادقة
- React Router DOM للتنقل
- React Hook Form + Zod للنماذج

## قاعدة البيانات

### الجداول الرئيسية:
1. **users** - بيانات المستخدمين
2. **conversations** - المحادثات
3. **messages** - الرسائل مع نظام المراقبة
4. **reports** - البلاغات
5. **matches** - المطابقات
6. **admin_logs** - سجلات الإدارة

### الأمان:
- تشفير كلمات المرور
- JWT للمصادقة
- Row Level Security (RLS)
- مراقبة المحتوى التلقائية

## اختبار الوظائف

### 1. التسجيل:
- انتقل إلى `/register`
- املأ النموذج بالبيانات المطلوبة
- تحقق من حفظ البيانات في قاعدة البيانات

### 2. تسجيل الدخول:
- انتقل إلى `/login`
- استخدم البريد الإلكتروني وكلمة المرور
- تحقق من التوجيه إلى الملف الشخصي

### 3. البحث:
- انتقل إلى `/search`
- جرب الفلاتر المختلفة
- تحقق من عرض النتائج من قاعدة البيانات

### 4. المراسلات:
- انتقل إلى `/messages`
- جرب إرسال رسالة
- تحقق من نظام المراقبة

### 5. لوحة التحكم:
- انتقل إلى `/admin`
- استكشف الإحصائيات والإدارة
- جرب مراجعة البلاغات

## الدعم والمساعدة

### المشاكل الشائعة:
1. **خطأ في الاتصال بقاعدة البيانات:**
   - تحقق من ملف `.env.local`
   - تأكد من صحة URL ومفتاح Supabase

2. **مشاكل في التسجيل:**
   - تحقق من صحة البيانات المدخلة
   - تأكد من عدم وجود البريد الإلكتروني مسبقاً

3. **مشاكل في البحث:**
   - تحقق من وجود بيانات في قاعدة البيانات
   - جرب البحث بدون فلاتر أولاً

### للمطورين:
- جميع الكود موثق ومنظم
- استخدم TypeScript للتحقق من الأنواع
- اتبع معايير الكود المتبعة في المشروع

## الخطوات التالية

### للإنتاج:
1. إعداد نطاق مخصص
2. تفعيل HTTPS
3. إعداد النسخ الاحتياطية
4. مراقبة الأداء
5. إعداد CDN للملفات الثابتة

### تحسينات مستقبلية:
1. تطبيق الهاتف المحمول
2. إشعارات فورية
3. نظام دفع متقدم
4. تحليلات مفصلة
5. دعم لغات إضافية

---

**المشروع جاهز للاستخدام والاختبار! 🎉**
